package utility

import (
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestParseDataURI 測試 parseDataURI 函數
func TestParseDataURI(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試有效的圖片 data URI
		imageDataURI := "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
		mimeType, base64Data, err := ParseDataURI(imageDataURI)
		t.AssertNil(err)
		t.Assert(mimeType, "image/png")
		t.Assert(base64Data, "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")

		// 測試有效的音頻 data URI
		audioDataURI := "data:audio/wav;base64,UklGRr7..."
		mimeType, base64Data, err = ParseDataURI(audioDataURI)
		t.AssertNil(err)
		t.Assert(mimeType, "audio/wav")
		t.Assert(base64Data, "UklGRr7...")

		// 測試空字符串
		_, _, err = ParseDataURI("")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "data URI cannot be empty")

		// 測試無效格式：不以 data: 開頭
		_, _, err = ParseDataURI("invalid:image/png;base64,data")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "invalid data URI format: must start with 'data:'")

		// 測試無效格式：缺少逗號
		_, _, err = ParseDataURI("data:image/png;base64data")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "invalid data URI format: missing comma separator")

		// 測試無效格式：缺少 base64 標識
		_, _, err = ParseDataURI("data:image/png,data")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "invalid data URI format: missing base64 indicator")

		// 測試無效編碼格式
		_, _, err = ParseDataURI("data:image/png;base32,data")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "unsupported encoding: base32, only base64 is supported")

		// 測試空 MIME 類型
		_, _, err = ParseDataURI("data:;base64,data")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "MIME type cannot be empty")

		// 測試空 base64 數據
		_, _, err = ParseDataURI("data:image/png;base64,")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "base64 data cannot be empty")

		t.Logf("ParseDataURI tests completed successfully")
	})
}

// TestSaveBase64ToTempFile 測試 saveBase64ToTempFile 函數
func TestSaveBase64ToTempFile(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()

		// 測試有效的圖片 base64 數據（1x1 像素的透明 PNG）
		imageBase64 := "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
		localPath, err := SaveBase64ToTempFile(ctx, imageBase64, "image/png", "image")
		t.AssertNil(err)
		t.AssertNE(localPath, "")
		t.Logf("Saved image to: %s", localPath)

		// 測試空 base64 數據
		_, err = SaveBase64ToTempFile(ctx, "", "image/png", "image")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "base64 data cannot be empty")

		// 測試空 MIME 類型
		_, err = SaveBase64ToTempFile(ctx, imageBase64, "", "image")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "MIME type cannot be empty")

		// 測試空消息類型
		_, err = SaveBase64ToTempFile(ctx, imageBase64, "image/png", "")
		t.AssertNE(err, nil)
		t.Assert(err.Error(), "message type cannot be empty")

		// 測試無效的 base64 數據
		_, err = SaveBase64ToTempFile(ctx, "invalid-base64!", "image/png", "image")
		t.AssertNE(err, nil)

		t.Logf("SaveBase64ToTempFile tests completed successfully")
	})
}
