package utility

import (
	lineChat "brainHub/api/brain/v1"
	omniChat "brainHub/api/omnichannel/v1"
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"context"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/gogf/gf/v2/encoding/gbase64"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gproc"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/grand"
)

// ConvertFileToMarkdown 將文件轉換為 Markdown 格式
// 如果文件不是文本或圖片格式，且系統中存在 markitdown 工具，則進行轉換
//
// 參數:
//   - ctx: 上下文
//   - filePath: 原始文件路徑
//   - mimeType: 文件的 MIME 類型
//
// 返回:
//   - convertedPath: 轉換後的文件路徑（如果轉換成功）或原始文件路徑
//   - isConverted: 是否進行了轉換
//   - shouldSkip: 是否應該跳過此文件（轉換失敗時）
//   - err: 轉換過程中的錯誤
func ConvertFileToMarkdown(ctx context.Context, filePath, mimeType string) (convertedPath string, isConverted bool, shouldSkip bool, err error) {
	// 檢查文件是否存在
	if !gfile.Exists(filePath) {
		err = gerror.Newf("file does not exist: %s", filePath)
		g.Log().Error(ctx, "ConvertFileToMarkdown file check failed:", err)
		return "", false, true, err
	}

	// 檢查是否為文本文件，如果是則直接返回（包括 markdown）
	if isTextFile(mimeType) {
		g.Log().Debugf(ctx, "File is text format, no conversion needed: %s (MIME: %s)", filePath, mimeType)
		return filePath, false, false, nil
	}

	// 檢查是否為圖片文件，如果是則直接返回（圖片不需要轉換）
	if isImageFile(mimeType) {
		g.Log().Debugf(ctx, "File is image format, no conversion needed: %s (MIME: %s)", filePath, mimeType)
		return filePath, false, false, nil
	}

	// 檢查系統中是否存在 markitdown 工具
	if g.IsEmpty(gproc.SearchBinary("markitdown")) {
		g.Log().Warningf(ctx, "markitdown tool not found, skipping file conversion: %s", filePath)
		return "", false, true, gerror.New("markitdown tool not available")
	}

	// 生成轉換後的文件路徑
	mdFileName := gfile.Name(filePath) + ".md"
	mdFilePath := gfile.Join(gfile.Dir(filePath), mdFileName)
	// 如md 的文件已經存在則無需要轉換直接返回
	if gfile.Exists(mdFilePath) {
		g.Log().Warningf(ctx, "md file already exists, no conversion needed: %s", mdFilePath)
		return mdFilePath, false, false, nil
	}

	// 執行 markitdown 轉換命令
	cmd := fmt.Sprintf("markitdown %q -o %q", filePath, mdFilePath)
	g.Log().Debugf(ctx, "Executing markitdown conversion: %s", cmd)

	_, err = gproc.ShellExec(ctx, cmd)
	if err != nil {
		g.Log().Errorf(ctx, "markitdown conversion failed for file %s (MIME: %s), command: %s, error: %v",
			filePath, mimeType, cmd, err)
		return "", false, true, err
	}

	// 檢查轉換後的文件是否存在且不為空
	if !gfile.Exists(mdFilePath) || gfile.Size(mdFilePath) == 0 {
		g.Log().Errorf(ctx, "markitdown conversion produced empty or missing output file: %s -> %s", filePath, mdFilePath)
		return "", false, true, gerror.New("conversion produced empty or missing file")
	}

	g.Log().Infof(ctx, "Successfully converted file to markdown: %s -> %s (MIME: %s)", filePath, mdFilePath, mimeType)
	return mdFilePath, true, false, nil
}

// isTextFile 檢查 MIME 類型是否為文本格式
// 所有以 text/ 開頭的 MIME 類型都被視為文本文件，不需要轉換
func isTextFile(mimeType string) bool {
	return gstr.HasPrefix(mimeType, "text/")
}

// isImageFile 檢查 MIME 類型是否為圖片格式
func isImageFile(mimeType string) bool {
	imageTypes := []string{
		"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
		"image/bmp", "image/tiff", "image/svg+xml", "image/heic", "image/heif",
	}

	for _, imageType := range imageTypes {
		if mimeType == imageType {
			return true
		}
	}
	return false
}
func ParseJsonFormatResponse(generateResponse string) string {
	// 只移除首尾的空白字符，保留內容中的空格
	var str = gstr.Trim(generateResponse)
	if gstr.ContainsI(str, "```json") {
		str = gstr.SubStrFrom(str, "```json")
		str = gstr.TrimLeftStr(str, "```json\n")
		str = gstr.TrimLeftStr(str, "```json")
		str = gstr.TrimRightStr(str, "\n```")
		str = gstr.TrimRightStr(str, "```")
		// 再次移除首尾空白字符，但保留內容中的空格
		str = gstr.Trim(str)
	}

	return str
}

// ConvertLLMParamToGeminiParams 將LLM參數轉換為Gemini參數配置
//
// 參數:
//   - llmParam: LLM參數模型，包含基本配置信息
//   - tenantParams: 租戶參數映射，用於替換特定配置中的租戶ID
//
// 返回:
//   - vertexParam: 轉換後的Gemini配置參數
//   - err: 處理過程中可能發生的錯誤
//
// 功能說明:
//
//	此函數將通用LLM參數轉換為Gemini特定配置，處理區域、項目ID、
//	溫度、令牌限制等關鍵參數，並支援租戶ID的動態替換
func ConvertLLMParamToGeminiParams(llmParam *model.LLMParams, tenantParams g.Map) (vertexParam *llm.LLMsConfig, err error) {
	if llmParam == nil {
		return nil, gerror.New("the llm param is nil")
	}
	if llmParam.LLMType != consts.VertexAIGemini {
		return nil, gerror.Newf("the llm type [%v] is not supported", llmParam.LLMType)
	}

	region := gjson.New(llmParam.BaseUrl).Get("region").String()
	if g.IsEmpty(region) {
		return nil, gerror.New("the region is empty")
	}

	projectID := gjson.New(llmParam.BaseUrl).Get("project_id").String()
	if g.IsEmpty(projectID) {
		return nil, gerror.New("the project id is empty")
	}

	if gstr.ToLower(projectID) == "$tenant_id" {
		projectID = gconv.String(tenantParams["tenant_id"])
	}
	includeThoughts := false

	includeThoughts = gjson.New(llmParam.BaseUrl).Get("include_thoughts").Bool()
	thinkingBudget := gjson.New(llmParam.BaseUrl).Get("thinking_budget").Int32()

	vertexParam = &llm.LLMsConfig{}
	vertexParam.Vertex.LLMType = consts.VertexAIGemini
	vertexParam.Common.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)
	vertexParam.Common.Temperature = llmParam.Temperature
	vertexParam.Vertex.Region = region
	vertexParam.Vertex.ProjectID = projectID
	vertexParam.Vertex.CredentialFile = projectID + ".json"
	vertexParam.Vertex.Gemini.Model = llmParam.ModelId
	vertexParam.Vertex.Gemini.Temperature = llmParam.Temperature
	vertexParam.Vertex.Gemini.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)

	vertexParam.Vertex.Gemini.IncludeThoughts = includeThoughts
	vertexParam.Vertex.Gemini.ThinkingBudget = thinkingBudget

	return vertexParam, nil
}

func ConvertLLMParamsToVertexParams(llmParam *model.LLMParams, tenantParams g.Map) (vertexParam *llm.LLMsConfig, err error) {
	if llmParam == nil {
		return nil, gerror.New("the llm param is nil")
	}
	if llmParam.LLMType != consts.VertexAIClaude {
		return nil, gerror.Newf("the llm type [%v] is not supported", llmParam.LLMType)
	}

	region := gjson.New(llmParam.BaseUrl).Get("region").String()
	if g.IsEmpty(region) {
		return nil, gerror.New("the region is empty")
	}

	projectID := gjson.New(llmParam.BaseUrl).Get("project_id").String()
	if g.IsEmpty(projectID) {
		return nil, gerror.New("the project id is empty")
	}

	if gstr.ToLower(projectID) == "$tenant_id" {
		projectID = gconv.String(tenantParams["tenant_id"])
	}

	vertexParam = &llm.LLMsConfig{}
	vertexParam.Vertex.LLMType = consts.VertexAIClaude
	vertexParam.Common.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)
	vertexParam.Common.Temperature = llmParam.Temperature
	vertexParam.Vertex.Region = region
	vertexParam.Vertex.ProjectID = projectID
	vertexParam.Vertex.CredentialFile = projectID + ".json"

	vertexParam.Vertex.ThirdModel.Model = llmParam.ModelId
	vertexParam.Vertex.ThirdModel.Temperature = llmParam.Temperature
	vertexParam.Vertex.ThirdModel.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)
	vertexParam.Vertex.ThirdModel.APIVersion = llmParam.APIVersion

	return
}

func ConvertAnyToGenericMessage(data any) *model.GenericMessage {
	nowTime := time.Now()
	var message = &model.GenericMessage{
		CrateAt: nowTime,
	}
	switch v := data.(type) {
	default:
		return nil

	case *lineChat.ChatReq:
		message.Message = v.Message
		message.ServiceID = v.ServiceID
		message.UserID = v.UserID
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentTypeText
		message.Channel = v.Channel
		message.Role = consts.RoleUser
		message.DisplayName = v.DisplayName

	case *lineChat.ChatWithAttachmentReq:
		message.ServiceID = v.ServiceID
		message.UserID = v.UserID
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentMediaFile
		message.Channel = v.Channel
		message.Role = consts.RoleUser
	case *omniChat.ChatReq:
		message.Message = v.Question
		message.ServiceID = v.ServiceId
		message.UserID = v.UserId
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentTypeText
		message.Channel = v.Channel
		message.Role = consts.RoleUser

	case *llm.ResponseData:
		message.Message = gconv.String(v.Response)
		message.ServiceID = v.ServiceID
		message.UserID = v.UserID
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentTypeText
		message.Channel = v.Channel
		message.Role = consts.RoleAi
	}

	return message
}

// DownloadFileFromURL 從 URL 下載文件到臨時目錄
// 支援 voice 和 image 類型文件的下載，並返回本地文件路徑和 MIME 類型
//
// 參數:
//   - ctx: 上下文
//   - url: 文件下載 URL
//   - messageType: 消息類型 (voice, image)
//
// 返回:
//   - localPath: 下載後的本地文件路徑
//   - mimeType: 檢測到的 MIME 類型
//   - err: 下載過程中的錯誤
func DownloadFileFromURL(ctx context.Context, url, messageType string) (localPath, mimeType string, err error) {
	if g.IsEmpty(url) {
		err = gerror.New("download URL cannot be empty")
		g.Log().Error(ctx, "DownloadFileFromURL URL validation failed:", err)
		return "", "", err
	}

	if g.IsEmpty(messageType) {
		err = gerror.New("message type cannot be empty")
		g.Log().Error(ctx, "DownloadFileFromURL message type validation failed:", err)
		return "", "", err
	}

	g.Log().Infof(ctx, "Starting file download from URL: %s, type: %s", url, messageType)

	// 創建 HTTP 客戶端（使用 GoFrame 的 gclient）
	client := gclient.New()
	client.SetTimeout(30 * time.Second) // 設置 30 秒超時

	// 下載文件
	response, err := client.Get(ctx, url)
	if err != nil {
		err = gerror.Wrapf(err, "failed to download file from URL: %s", url)
		g.Log().Error(ctx, "DownloadFileFromURL HTTP request failed:", err)
		return "", "", err
	}
	defer response.Close()

	// 檢查 HTTP 狀態碼
	if response.StatusCode != http.StatusOK {
		err = gerror.Newf("HTTP request failed with status: %d %s", response.StatusCode, response.Status)
		g.Log().Error(ctx, "DownloadFileFromURL HTTP status error:", err)
		return "", "", err
	}

	// 讀取文件內容
	fileContent, err := io.ReadAll(response.Body)
	if err != nil {
		err = gerror.Wrapf(err, "failed to read response body from URL: %s", url)
		g.Log().Error(ctx, "DownloadFileFromURL read body failed:", err)
		return "", "", err
	}

	// 檢測 MIME 類型
	detectedMime := mimetype.Detect(fileContent)
	mimeType = detectedMime.String()
	g.Log().Debugf(ctx, "Detected MIME type: %s for URL: %s", mimeType, url)

	// 驗證文件類型是否符合預期
	if !IsValidFileType(mimeType, messageType) {
		err = gerror.Newf("invalid file type: expected %s, got %s", messageType, mimeType)
		g.Log().Error(ctx, "DownloadFileFromURL file type validation failed:", err)
		return "", "", err
	}

	// 生成臨時文件名
	tempDir := gfile.Temp()
	fileName := fmt.Sprintf("%s_%d%s", messageType, grand.N(100000, 999999), detectedMime.Extension())
	localPath = filepath.Join(tempDir, fileName)

	// 保存文件到臨時目錄
	err = gfile.PutBytes(localPath, fileContent)
	if err != nil {
		err = gerror.Wrapf(err, "failed to save file to: %s", localPath)
		g.Log().Error(ctx, "DownloadFileFromURL save file failed:", err)
		return "", "", err
	}

	g.Log().Infof(ctx, "Successfully downloaded file: %s -> %s (MIME: %s, Size: %d bytes)",
		url, localPath, mimeType, len(fileContent))

	return localPath, mimeType, nil
}

// IsValidFileType 驗證文件類型是否符合消息類型
func IsValidFileType(mimeType, messageType string) bool {
	switch messageType {
	case consts.MessageTypeImage:
		// 檢查是否為支援的圖片格式
		for _, format := range consts.PhotoFormat {
			if mimeType == format {
				return true
			}
		}
		return false
	case consts.MessageTypeVoice:
		// 檢查是否為支援的音頻格式
		for _, format := range consts.AudioFormat {
			if mimeType == format {
				return true
			}
		}
		return false
	default:
		return false
	}
}

// parseDataURI 解析 data URI 格式，提取 MIME 類型和 base64 數據
// 輸入格式：data:image/png;base64,iVBORw0KGgoAAA...
// 返回：mimeType="image/png", base64Data="iVBORw0KGgoAAA...", err=nil
func ParseDataURI(dataURI string) (mimeType, base64Data string, err error) {
	if g.IsEmpty(dataURI) {
		err = gerror.New("data URI cannot be empty")
		return "", "", err
	}

	// 檢查是否以 "data:" 開頭
	if !gstr.HasPrefix(dataURI, "data:") {
		err = gerror.New("invalid data URI format: must start with 'data:'")
		return "", "", err
	}

	// 移除 "data:" 前綴
	content := gstr.SubStr(dataURI, 5)

	// 查找第一個逗號，分割 metadata 和 base64 數據
	commaIndex := gstr.Pos(content, ",")
	if commaIndex == -1 {
		err = gerror.New("invalid data URI format: missing comma separator")
		return "", "", err
	}

	metadata := gstr.SubStr(content, 0, commaIndex)
	base64Data = gstr.SubStr(content, commaIndex+1)

	// 解析 metadata，格式：mimeType;base64
	parts := gstr.Split(metadata, ";")
	if len(parts) < 2 {
		err = gerror.New("invalid data URI format: missing base64 indicator")
		return "", "", err
	}

	mimeType = parts[0]
	encoding := parts[1]

	// 驗證編碼格式
	if encoding != "base64" {
		err = gerror.Newf("unsupported encoding: %s, only base64 is supported", encoding)
		return "", "", err
	}

	// 驗證 MIME 類型不為空
	if g.IsEmpty(mimeType) {
		err = gerror.New("MIME type cannot be empty")
		return "", "", err
	}

	// 驗證 base64 數據不為空
	if g.IsEmpty(base64Data) {
		err = gerror.New("base64 data cannot be empty")
		return "", "", err
	}

	return mimeType, base64Data, nil
}

// SaveBase64ToTempFile 將 base64 數據保存為臨時文件
// 用於語音處理流程
//
// 參數:
//   - ctx: 上下文
//   - base64Data: 純 base64 編碼的數據（不包含 data URI 前綴）
//   - mimeType: MIME 類型（如 "audio/wav"）
//   - messageType: 消息類型（如 "voice"）
//
// 返回:
//   - localPath: 臨時文件的本地路徑
//   - err: 處理過程中的錯誤
func SaveBase64ToTempFile(ctx context.Context, base64Data, mimeType, messageType string) (localPath string, err error) {
	if g.IsEmpty(base64Data) {
		err = gerror.New("base64 data cannot be empty")
		g.Log().Error(ctx, "SaveBase64ToTempFile base64 data validation failed:", err)
		return "", err
	}

	if g.IsEmpty(mimeType) {
		err = gerror.New("MIME type cannot be empty")
		g.Log().Error(ctx, "SaveBase64ToTempFile MIME type validation failed:", err)
		return "", err
	}

	if g.IsEmpty(messageType) {
		err = gerror.New("message type cannot be empty")
		g.Log().Error(ctx, "SaveBase64ToTempFile message type validation failed:", err)
		return "", err
	}

	g.Log().Infof(ctx, "Converting base64 to temp file: type=%s, mime=%s", messageType, mimeType)

	// 解碼 base64 數據
	fileContent, err := gbase64.DecodeString(base64Data)
	if err != nil {
		err = gerror.Wrapf(err, "failed to decode base64 data")
		g.Log().Error(ctx, "SaveBase64ToTempFile base64 decode failed:", err)
		return "", err
	}

	// 檢測文件擴展名
	detectedMime := mimetype.Detect(fileContent)
	extension := detectedMime.Extension()

	// 如果檢測到的 MIME 類型與提供的不匹配，記錄警告但繼續處理
	if detectedMime.String() != mimeType {
		g.Log().Warningf(ctx, "MIME type mismatch: provided=%s, detected=%s", mimeType, detectedMime.String())
	}

	// 驗證文件類型是否符合消息類型
	if !IsValidFileType(mimeType, messageType) {
		err = gerror.Newf("invalid file type: expected %s, got %s", messageType, mimeType)
		g.Log().Error(ctx, "SaveBase64ToTempFile file type validation failed:", err)
		return "", err
	}

	// 生成臨時文件名
	tempDir := gfile.Temp()
	fileName := fmt.Sprintf("%s_%d%s", messageType, grand.N(100000, 999999), extension)
	localPath = filepath.Join(tempDir, fileName)

	// 保存文件到臨時目錄
	err = gfile.PutBytes(localPath, fileContent)
	if err != nil {
		err = gerror.Wrapf(err, "failed to save file to: %s", localPath)
		g.Log().Error(ctx, "SaveBase64ToTempFile save file failed:", err)
		return "", err
	}

	g.Log().Infof(ctx, "Successfully saved base64 to temp file: %s (MIME: %s, Size: %d bytes)",
		localPath, mimeType, len(fileContent))

	return localPath, nil
}

// 測試相關的輔助函數可以在這裡添加
