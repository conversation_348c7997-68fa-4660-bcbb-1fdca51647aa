package test

import (
	"brainHub/internal/service"
	"testing"

	// 導入 logic 包以確保 init 函數被執行
	_ "brainHub/internal/logic"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestEmbeddingServiceRegistration 測試 embedding 服務是否正確註冊
func TestEmbeddingServiceRegistration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試服務是否已註冊（不應該 panic）
		embeddingService := service.Embedding()
		t.AssertNE(embeddingService, nil)

		// 測試服務方法是否可用
		providers := embeddingService.GetSupportedProviders()
		t.AssertGT(len(providers), 0)
		t.AssertIN("aoai", providers)
	})
}
