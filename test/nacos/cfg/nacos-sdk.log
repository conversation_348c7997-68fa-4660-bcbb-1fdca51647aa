2025-07-07T16:46:50.015+0800	WARN	config_client/config_client.go:335	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:46:50.015+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:46:50.015+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-013a79bc-66d0-43e4-a343-50950aa30bcf)
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:46:50.015+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-013a79bc-66d0-43e4-a343-50950aa30bcf try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:46:50.015+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:46:50.015+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:46:50.015+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:46:50.140+0800	INFO	rpc/rpc_client.go:337	config-0-013a79bc-66d0-43e4-a343-50950aa30bcf success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878010126_192.168.3.3_61362
2025-07-07T16:46:50.140+0800	INFO	rpc/rpc_client.go:486	config-0-013a79bc-66d0-43e4-a343-50950aa30bcf notify connected event to listeners , connectionId=1751878010126_192.168.3.3_61362
2025-07-07T16:46:50.141+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:53:51.930+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:53:51.931+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-ca3d12db-3993-498a-9f78-16d63b862c72)
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:53:51.931+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-ca3d12db-3993-498a-9f78-16d63b862c72 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:53:51.931+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:53:51.931+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:53:51.931+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:53:52.049+0800	INFO	rpc/rpc_client.go:337	config-0-ca3d12db-3993-498a-9f78-16d63b862c72 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878432012_192.168.3.3_62029
2025-07-07T16:53:52.050+0800	INFO	rpc/rpc_client.go:486	config-0-ca3d12db-3993-498a-9f78-16d63b862c72 notify connected event to listeners , connectionId=1751878432012_192.168.3.3_62029
2025-07-07T16:53:52.050+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:54:39.201+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:54:39.202+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-a7219eea-a68f-4213-a629-05adb2195eb6)
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:54:39.202+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-a7219eea-a68f-4213-a629-05adb2195eb6 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:54:39.202+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:54:39.202+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:54:39.202+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:54:39.326+0800	INFO	rpc/rpc_client.go:337	config-0-a7219eea-a68f-4213-a629-05adb2195eb6 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878479289_192.168.3.3_62130
2025-07-07T16:54:39.329+0800	INFO	rpc/rpc_client.go:486	config-0-a7219eea-a68f-4213-a629-05adb2195eb6 notify connected event to listeners , connectionId=1751878479289_192.168.3.3_62130
2025-07-07T16:54:39.329+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:55:24.521+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:55:24.521+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d)
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:55:24.522+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:55:24.522+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:55:24.522+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:55:24.522+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:55:24.647+0800	INFO	rpc/rpc_client.go:337	config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878524610_192.168.3.3_62221
2025-07-07T16:55:24.647+0800	INFO	rpc/rpc_client.go:486	config-0-625567c4-e98f-4d33-9f71-914ae9f7d81d notify connected event to listeners , connectionId=1751878524610_192.168.3.3_62221
2025-07-07T16:55:24.647+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-07T16:56:32.397+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-07T16:56:32.398+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-ba565615-185a-4b7a-8061-04975ca6fc89)
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-07T16:56:32.398+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-ba565615-185a-4b7a-8061-04975ca6fc89 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-07T16:56:32.398+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-07T16:56:32.398+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-07T16:56:32.398+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-07T16:56:32.522+0800	INFO	rpc/rpc_client.go:337	config-0-ba565615-185a-4b7a-8061-04975ca6fc89 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751878592485_192.168.3.3_62342
2025-07-07T16:56:32.523+0800	INFO	rpc/rpc_client.go:486	config-0-ba565615-185a-4b7a-8061-04975ca6fc89 notify connected event to listeners , connectionId=1751878592485_192.168.3.3_62342
2025-07-07T16:56:32.523+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-17T16:45:12.686+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-17T16:45:12.687+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-771d20a5-909c-46f9-8376-db278de855db)
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-17T16:45:12.687+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-771d20a5-909c-46f9-8376-db278de855db try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-17T16:45:12.687+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-17T16:45:12.687+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-17T16:45:12.687+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-17T16:45:12.803+0800	INFO	rpc/rpc_client.go:337	config-0-771d20a5-909c-46f9-8376-db278de855db success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752741912782_192.168.3.3_52946
2025-07-17T16:45:12.804+0800	INFO	rpc/rpc_client.go:486	config-0-771d20a5-909c-46f9-8376-db278de855db notify connected event to listeners , connectionId=1752741912782_192.168.3.3_52946
2025-07-17T16:45:12.804+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-08-05T14:04:22.952+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-08-05T14:04:22.953+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-49570744-85e8-422c-8ed5-6dbb153b0a40)
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-05T14:04:22.953+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-49570744-85e8-422c-8ed5-6dbb153b0a40 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-05T14:04:22.953+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-05T14:04:22.953+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-05T14:04:22.953+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-05T14:04:23.077+0800	INFO	rpc/rpc_client.go:337	config-0-49570744-85e8-422c-8ed5-6dbb153b0a40 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1754373863006_192.168.3.5_60930
2025-08-05T14:04:23.077+0800	INFO	rpc/rpc_client.go:486	config-0-49570744-85e8-422c-8ed5-6dbb153b0a40 notify connected event to listeners , connectionId=1754373863006_192.168.3.5_60930
2025-08-05T14:04:23.077+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:06:01.745+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:06:01.746+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-d9d7aea2-1b3b-4038-8c89-bf35eb497b17)
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:06:01.746+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-d9d7aea2-1b3b-4038-8c89-bf35eb497b17 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:06:01.746+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:06:01.746+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:06:01.746+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:06:01.866+0800	INFO	rpc/rpc_client.go:337	config-0-d9d7aea2-1b3b-4038-8c89-bf35eb497b17 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096361850_192.168.3.254_60950
2025-09-17T16:06:01.866+0800	INFO	rpc/rpc_client.go:486	config-0-d9d7aea2-1b3b-4038-8c89-bf35eb497b17 notify connected event to listeners , connectionId=1758096361850_192.168.3.254_60950
2025-09-17T16:06:01.866+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:10:55.968+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:10:55.969+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-db564c8b-c359-4139-a313-98733f0d0ea8)
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:10:55.969+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-db564c8b-c359-4139-a313-98733f0d0ea8 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:10:55.969+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:10:55.969+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:10:55.969+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:10:56.096+0800	INFO	rpc/rpc_client.go:337	config-0-db564c8b-c359-4139-a313-98733f0d0ea8 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096656079_192.168.3.254_61629
2025-09-17T16:10:56.096+0800	INFO	rpc/rpc_client.go:486	config-0-db564c8b-c359-4139-a313-98733f0d0ea8 notify connected event to listeners , connectionId=1758096656079_192.168.3.254_61629
2025-09-17T16:10:56.097+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:11:42.430+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:11:42.430+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-6f990cb1-a013-49af-a4f8-d60df611c252)
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:11:42.430+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-6f990cb1-a013-49af-a4f8-d60df611c252 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:11:42.431+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:11:42.431+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:11:42.431+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:11:42.551+0800	INFO	rpc/rpc_client.go:337	config-0-6f990cb1-a013-49af-a4f8-d60df611c252 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096702533_192.168.3.254_61752
2025-09-17T16:11:42.551+0800	INFO	rpc/rpc_client.go:486	config-0-6f990cb1-a013-49af-a4f8-d60df611c252 notify connected event to listeners , connectionId=1758096702533_192.168.3.254_61752
2025-09-17T16:11:42.551+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:12:10.629+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:12:10.629+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-523e2839-346b-4b10-be64-55d74abf4c0d)
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:12:10.629+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-523e2839-346b-4b10-be64-55d74abf4c0d try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:12:10.630+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:12:10.630+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:12:10.630+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:12:10.753+0800	INFO	rpc/rpc_client.go:337	config-0-523e2839-346b-4b10-be64-55d74abf4c0d success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096730738_192.168.3.254_61825
2025-09-17T16:12:10.754+0800	INFO	rpc/rpc_client.go:486	config-0-523e2839-346b-4b10-be64-55d74abf4c0d notify connected event to listeners , connectionId=1758096730738_192.168.3.254_61825
2025-09-17T16:12:10.754+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T16:14:51.369+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T16:14:51.369+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-87a7cc62-4470-4c26-a7d2-2d63ee5030d0)
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T16:14:51.369+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-87a7cc62-4470-4c26-a7d2-2d63ee5030d0 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T16:14:51.369+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T16:14:51.369+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T16:14:51.369+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T16:14:51.493+0800	INFO	rpc/rpc_client.go:337	config-0-87a7cc62-4470-4c26-a7d2-2d63ee5030d0 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758096891475_192.168.3.254_62045
2025-09-17T16:14:51.494+0800	INFO	rpc/rpc_client.go:486	config-0-87a7cc62-4470-4c26-a7d2-2d63ee5030d0 notify connected event to listeners , connectionId=1758096891475_192.168.3.254_62045
2025-09-17T16:14:51.494+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T17:04:21.336+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T17:04:21.336+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-890aaa6d-2c46-40ca-8559-882e54e12e4f)
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T17:04:21.336+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-890aaa6d-2c46-40ca-8559-882e54e12e4f try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T17:04:21.336+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T17:04:21.336+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T17:04:21.336+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T17:04:21.454+0800	INFO	rpc/rpc_client.go:337	config-0-890aaa6d-2c46-40ca-8559-882e54e12e4f success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758099861421_192.168.3.254_64887
2025-09-17T17:04:21.455+0800	INFO	rpc/rpc_client.go:486	config-0-890aaa6d-2c46-40ca-8559-882e54e12e4f notify connected event to listeners , connectionId=1758099861421_192.168.3.254_64887
2025-09-17T17:04:21.455+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T17:05:02.403+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T17:05:02.403+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-72529dc3-5335-4009-b31c-5cbf6f821a39)
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T17:05:02.403+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-72529dc3-5335-4009-b31c-5cbf6f821a39 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T17:05:02.403+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T17:05:02.403+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T17:05:02.403+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T17:05:02.526+0800	INFO	rpc/rpc_client.go:337	config-0-72529dc3-5335-4009-b31c-5cbf6f821a39 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758099902492_192.168.3.254_64978
2025-09-17T17:05:02.526+0800	INFO	rpc/rpc_client.go:486	config-0-72529dc3-5335-4009-b31c-5cbf6f821a39 notify connected event to listeners , connectionId=1758099902492_192.168.3.254_64978
2025-09-17T17:05:02.526+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T18:28:17.238+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T18:28:17.239+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-8118a386-c270-4eff-9ed2-e8f85ea25ee2)
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T18:28:17.239+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-8118a386-c270-4eff-9ed2-e8f85ea25ee2 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T18:28:17.239+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T18:28:17.239+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T18:28:17.239+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T18:28:17.358+0800	INFO	rpc/rpc_client.go:337	config-0-8118a386-c270-4eff-9ed2-e8f85ea25ee2 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758104897310_192.168.3.254_54861
2025-09-17T18:28:17.359+0800	INFO	rpc/rpc_client.go:486	config-0-8118a386-c270-4eff-9ed2-e8f85ea25ee2 notify connected event to listeners , connectionId=1758104897310_192.168.3.254_54861
2025-09-17T18:28:17.359+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:31:28.526+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:31:28.527+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:31:28.527+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-23d6fcef-e056-4dd6-8486-f01e398efc1c)
2025-09-17T20:31:28.527+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:31:28.527+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:31:28.527+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:31:28.527+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-23d6fcef-e056-4dd6-8486-f01e398efc1c try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:31:28.527+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:31:28.527+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:31:28.527+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:31:28.651+0800	INFO	rpc/rpc_client.go:337	config-0-23d6fcef-e056-4dd6-8486-f01e398efc1c success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112288610_192.168.3.254_64313
2025-09-17T20:31:28.652+0800	INFO	rpc/rpc_client.go:486	config-0-23d6fcef-e056-4dd6-8486-f01e398efc1c notify connected event to listeners , connectionId=1758112288610_192.168.3.254_64313
2025-09-17T20:31:28.652+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:31:37.865+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:31:37.865+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:31:37.865+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-56426908-8fd4-4971-beb1-1ae6020bc3f9)
2025-09-17T20:31:37.865+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:31:37.865+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:31:37.865+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:31:37.865+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-56426908-8fd4-4971-beb1-1ae6020bc3f9 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:31:37.865+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:31:37.865+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:31:37.865+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:31:37.984+0800	INFO	rpc/rpc_client.go:337	config-0-56426908-8fd4-4971-beb1-1ae6020bc3f9 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112297945_192.168.3.254_64346
2025-09-17T20:31:37.984+0800	INFO	rpc/rpc_client.go:486	config-0-56426908-8fd4-4971-beb1-1ae6020bc3f9 notify connected event to listeners , connectionId=1758112297945_192.168.3.254_64346
2025-09-17T20:31:37.984+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:32:00.176+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:32:00.176+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:32:00.176+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-df1909ac-11a8-4166-a098-d079fba5a6b9)
2025-09-17T20:32:00.176+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:32:00.176+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:32:00.176+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:32:00.176+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-df1909ac-11a8-4166-a098-d079fba5a6b9 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:32:00.176+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:32:00.176+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:32:00.176+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:32:00.293+0800	INFO	rpc/rpc_client.go:337	config-0-df1909ac-11a8-4166-a098-d079fba5a6b9 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112320254_192.168.3.254_64401
2025-09-17T20:32:00.293+0800	INFO	rpc/rpc_client.go:486	config-0-df1909ac-11a8-4166-a098-d079fba5a6b9 notify connected event to listeners , connectionId=1758112320254_192.168.3.254_64401
2025-09-17T20:32:00.293+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:32:16.112+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:32:16.112+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:32:16.112+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-d9ee5307-e889-4123-89cc-7df445c7e6db)
2025-09-17T20:32:16.112+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:32:16.112+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:32:16.112+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:32:16.112+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-d9ee5307-e889-4123-89cc-7df445c7e6db try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:32:16.112+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:32:16.112+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:32:16.112+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:32:16.232+0800	INFO	rpc/rpc_client.go:337	config-0-d9ee5307-e889-4123-89cc-7df445c7e6db success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112336193_192.168.3.254_64457
2025-09-17T20:32:16.232+0800	INFO	rpc/rpc_client.go:486	config-0-d9ee5307-e889-4123-89cc-7df445c7e6db notify connected event to listeners , connectionId=1758112336193_192.168.3.254_64457
2025-09-17T20:32:16.232+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:32:31.926+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:32:31.926+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:32:31.926+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b06ff244-c3dc-4905-9a1d-f3d74169abc0)
2025-09-17T20:32:31.926+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:32:31.926+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:32:31.926+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:32:31.926+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b06ff244-c3dc-4905-9a1d-f3d74169abc0 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:32:31.926+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:32:31.926+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:32:31.926+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:32:32.055+0800	INFO	rpc/rpc_client.go:337	config-0-b06ff244-c3dc-4905-9a1d-f3d74169abc0 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112352015_192.168.3.254_64509
2025-09-17T20:32:32.056+0800	INFO	rpc/rpc_client.go:486	config-0-b06ff244-c3dc-4905-9a1d-f3d74169abc0 notify connected event to listeners , connectionId=1758112352015_192.168.3.254_64509
2025-09-17T20:32:32.056+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:32:48.641+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:32:48.641+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:32:48.641+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-fce0893f-941a-4d6e-a8b2-c9e0f8cbd065)
2025-09-17T20:32:48.641+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:32:48.641+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:32:48.641+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:32:48.641+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-fce0893f-941a-4d6e-a8b2-c9e0f8cbd065 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:32:48.641+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:32:48.641+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:32:48.641+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:32:48.760+0800	INFO	rpc/rpc_client.go:337	config-0-fce0893f-941a-4d6e-a8b2-c9e0f8cbd065 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112368722_192.168.3.254_64561
2025-09-17T20:32:48.761+0800	INFO	rpc/rpc_client.go:486	config-0-fce0893f-941a-4d6e-a8b2-c9e0f8cbd065 notify connected event to listeners , connectionId=1758112368722_192.168.3.254_64561
2025-09-17T20:32:48.761+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:35:40.068+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:35:40.068+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:35:40.068+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-7ea80657-853b-470b-bbad-8c4e7d79a272)
2025-09-17T20:35:40.068+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:35:40.068+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:35:40.068+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:35:40.068+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-7ea80657-853b-470b-bbad-8c4e7d79a272 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:35:40.068+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:35:40.068+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:35:40.068+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:35:40.193+0800	INFO	rpc/rpc_client.go:337	config-0-7ea80657-853b-470b-bbad-8c4e7d79a272 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112540153_192.168.3.254_64872
2025-09-17T20:35:40.193+0800	INFO	rpc/rpc_client.go:486	config-0-7ea80657-853b-470b-bbad-8c4e7d79a272 notify connected event to listeners , connectionId=1758112540153_192.168.3.254_64872
2025-09-17T20:35:40.193+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:37:15.276+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:37:15.276+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:37:15.276+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-f32935e3-455c-43f1-8366-a942007fc7fc)
2025-09-17T20:37:15.276+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:37:15.276+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:37:15.276+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:37:15.276+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-f32935e3-455c-43f1-8366-a942007fc7fc try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:37:15.276+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:37:15.276+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:37:15.276+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:37:15.398+0800	INFO	rpc/rpc_client.go:337	config-0-f32935e3-455c-43f1-8366-a942007fc7fc success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112635357_192.168.3.254_65069
2025-09-17T20:37:15.399+0800	INFO	rpc/rpc_client.go:486	config-0-f32935e3-455c-43f1-8366-a942007fc7fc notify connected event to listeners , connectionId=1758112635357_192.168.3.254_65069
2025-09-17T20:37:15.399+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:37:33.595+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:37:33.595+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:37:33.595+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-e42b8af3-54b3-44a6-9009-2808c437f78e)
2025-09-17T20:37:33.595+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:37:33.595+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:37:33.595+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:37:33.595+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-e42b8af3-54b3-44a6-9009-2808c437f78e try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:37:33.595+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:37:33.595+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:37:33.595+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:37:33.717+0800	INFO	rpc/rpc_client.go:337	config-0-e42b8af3-54b3-44a6-9009-2808c437f78e success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112653677_192.168.3.254_65132
2025-09-17T20:37:33.717+0800	INFO	rpc/rpc_client.go:486	config-0-e42b8af3-54b3-44a6-9009-2808c437f78e notify connected event to listeners , connectionId=1758112653677_192.168.3.254_65132
2025-09-17T20:37:33.717+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:39:55.042+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:39:55.043+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:39:55.043+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-3822006c-a139-49a2-8c4f-ff4c8f464a15)
2025-09-17T20:39:55.043+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:39:55.043+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:39:55.043+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:39:55.043+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-3822006c-a139-49a2-8c4f-ff4c8f464a15 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:39:55.043+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:39:55.043+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:39:55.043+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:39:55.176+0800	INFO	rpc/rpc_client.go:337	config-0-3822006c-a139-49a2-8c4f-ff4c8f464a15 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112795125_192.168.3.254_65367
2025-09-17T20:39:55.176+0800	INFO	rpc/rpc_client.go:486	config-0-3822006c-a139-49a2-8c4f-ff4c8f464a15 notify connected event to listeners , connectionId=1758112795125_192.168.3.254_65367
2025-09-17T20:39:55.176+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:40:09.256+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:40:09.256+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:40:09.256+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-17b564e2-6078-44c1-b8c4-2d61deca1aba)
2025-09-17T20:40:09.256+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:40:09.256+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:40:09.256+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:40:09.256+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-17b564e2-6078-44c1-b8c4-2d61deca1aba try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:40:09.256+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:40:09.256+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:40:09.256+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:40:09.377+0800	INFO	rpc/rpc_client.go:337	config-0-17b564e2-6078-44c1-b8c4-2d61deca1aba success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112809338_192.168.3.254_65412
2025-09-17T20:40:09.377+0800	INFO	rpc/rpc_client.go:486	config-0-17b564e2-6078-44c1-b8c4-2d61deca1aba notify connected event to listeners , connectionId=1758112809338_192.168.3.254_65412
2025-09-17T20:40:09.377+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:40:21.949+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:40:21.950+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:40:21.950+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-048dca65-1c85-4c9b-8855-305ee34e6d0d)
2025-09-17T20:40:21.950+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:40:21.950+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:40:21.950+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:40:21.950+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-048dca65-1c85-4c9b-8855-305ee34e6d0d try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:40:21.950+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:40:21.950+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:40:21.950+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:40:22.072+0800	INFO	rpc/rpc_client.go:337	config-0-048dca65-1c85-4c9b-8855-305ee34e6d0d success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112822032_192.168.3.254_65447
2025-09-17T20:40:22.072+0800	INFO	rpc/rpc_client.go:486	config-0-048dca65-1c85-4c9b-8855-305ee34e6d0d notify connected event to listeners , connectionId=1758112822032_192.168.3.254_65447
2025-09-17T20:40:22.072+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:43:02.138+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:43:02.138+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:43:02.138+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-83435de2-ad01-4fb6-8627-65abb1949f92)
2025-09-17T20:43:02.138+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:43:02.138+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:43:02.138+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:43:02.138+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-83435de2-ad01-4fb6-8627-65abb1949f92 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:43:02.138+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:43:02.138+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:43:02.138+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:43:02.259+0800	INFO	rpc/rpc_client.go:337	config-0-83435de2-ad01-4fb6-8627-65abb1949f92 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758112982220_192.168.3.254_49280
2025-09-17T20:43:02.259+0800	INFO	rpc/rpc_client.go:486	config-0-83435de2-ad01-4fb6-8627-65abb1949f92 notify connected event to listeners , connectionId=1758112982220_192.168.3.254_49280
2025-09-17T20:43:02.259+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:44:54.418+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:44:54.419+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:44:54.419+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-479cd6b9-5460-4139-b3cb-5d5430cc79d6)
2025-09-17T20:44:54.419+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:44:54.419+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:44:54.419+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:44:54.419+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-479cd6b9-5460-4139-b3cb-5d5430cc79d6 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:44:54.419+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:44:54.419+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:44:54.419+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:44:54.539+0800	INFO	rpc/rpc_client.go:337	config-0-479cd6b9-5460-4139-b3cb-5d5430cc79d6 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758113094500_192.168.3.254_49593
2025-09-17T20:44:54.539+0800	INFO	rpc/rpc_client.go:486	config-0-479cd6b9-5460-4139-b3cb-5d5430cc79d6 notify connected event to listeners , connectionId=1758113094500_192.168.3.254_49593
2025-09-17T20:44:54.539+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:46:29.822+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:46:29.823+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:46:29.823+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-e8f162c9-f46e-419d-86df-c7483bf0238f)
2025-09-17T20:46:29.823+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:46:29.823+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:46:29.823+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:46:29.823+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-e8f162c9-f46e-419d-86df-c7483bf0238f try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:46:29.823+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:46:29.823+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:46:29.823+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:46:29.946+0800	INFO	rpc/rpc_client.go:337	config-0-e8f162c9-f46e-419d-86df-c7483bf0238f success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758113189906_192.168.3.254_49844
2025-09-17T20:46:29.946+0800	INFO	rpc/rpc_client.go:486	config-0-e8f162c9-f46e-419d-86df-c7483bf0238f notify connected event to listeners , connectionId=1758113189906_192.168.3.254_49844
2025-09-17T20:46:29.946+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:49:34.558+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:49:34.558+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:49:34.558+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-77fd4dde-bce1-4e0c-ae10-80836410bbab)
2025-09-17T20:49:34.558+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:49:34.558+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:49:34.558+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:49:34.558+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-77fd4dde-bce1-4e0c-ae10-80836410bbab try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:49:34.558+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:49:34.558+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:49:34.558+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:49:34.682+0800	INFO	rpc/rpc_client.go:337	config-0-77fd4dde-bce1-4e0c-ae10-80836410bbab success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758113374643_192.168.3.254_50062
2025-09-17T20:49:34.682+0800	INFO	rpc/rpc_client.go:486	config-0-77fd4dde-bce1-4e0c-ae10-80836410bbab notify connected event to listeners , connectionId=1758113374643_192.168.3.254_50062
2025-09-17T20:49:34.682+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:53:10.318+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:53:10.318+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:53:10.318+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-a6dbb325-bc82-4929-b755-c0f2b9f88e96)
2025-09-17T20:53:10.318+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:53:10.318+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:53:10.318+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:53:10.318+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-a6dbb325-bc82-4929-b755-c0f2b9f88e96 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:53:10.318+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:53:10.318+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:53:10.318+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:53:10.439+0800	INFO	rpc/rpc_client.go:337	config-0-a6dbb325-bc82-4929-b755-c0f2b9f88e96 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758113590400_192.168.3.254_50442
2025-09-17T20:53:10.440+0800	INFO	rpc/rpc_client.go:486	config-0-a6dbb325-bc82-4929-b755-c0f2b9f88e96 notify connected event to listeners , connectionId=1758113590400_192.168.3.254_50442
2025-09-17T20:53:10.440+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:56:39.405+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:56:39.405+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:56:39.405+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-c95a77f6-00c7-4b7b-ab0b-de59bdc07843)
2025-09-17T20:56:39.405+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:56:39.405+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:56:39.405+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:56:39.405+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-c95a77f6-00c7-4b7b-ab0b-de59bdc07843 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:56:39.405+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:56:39.405+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:56:39.405+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:56:39.524+0800	INFO	rpc/rpc_client.go:337	config-0-c95a77f6-00c7-4b7b-ab0b-de59bdc07843 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758113799484_192.168.3.254_50800
2025-09-17T20:56:39.524+0800	INFO	rpc/rpc_client.go:486	config-0-c95a77f6-00c7-4b7b-ab0b-de59bdc07843 notify connected event to listeners , connectionId=1758113799484_192.168.3.254_50800
2025-09-17T20:56:39.524+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T20:57:20.093+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T20:57:20.093+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T20:57:20.093+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-a287a314-78a7-490b-9b5a-8b97669b3c95)
2025-09-17T20:57:20.093+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T20:57:20.093+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T20:57:20.093+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T20:57:20.093+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-a287a314-78a7-490b-9b5a-8b97669b3c95 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T20:57:20.093+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T20:57:20.093+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T20:57:20.093+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T20:57:20.215+0800	INFO	rpc/rpc_client.go:337	config-0-a287a314-78a7-490b-9b5a-8b97669b3c95 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758113840175_192.168.3.254_50890
2025-09-17T20:57:20.215+0800	INFO	rpc/rpc_client.go:486	config-0-a287a314-78a7-490b-9b5a-8b97669b3c95 notify connected event to listeners , connectionId=1758113840175_192.168.3.254_50890
2025-09-17T20:57:20.215+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T21:10:40.325+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T21:10:40.325+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T21:10:40.325+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-0c43ba94-2843-46bc-ab1b-947cd3439a17)
2025-09-17T21:10:40.325+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T21:10:40.325+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T21:10:40.325+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T21:10:40.325+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-0c43ba94-2843-46bc-ab1b-947cd3439a17 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T21:10:40.325+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T21:10:40.325+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T21:10:40.325+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T21:10:40.449+0800	INFO	rpc/rpc_client.go:337	config-0-0c43ba94-2843-46bc-ab1b-947cd3439a17 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758114640408_192.168.3.254_52906
2025-09-17T21:10:40.449+0800	INFO	rpc/rpc_client.go:486	config-0-0c43ba94-2843-46bc-ab1b-947cd3439a17 notify connected event to listeners , connectionId=1758114640408_192.168.3.254_52906
2025-09-17T21:10:40.449+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T21:10:48.826+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T21:10:48.827+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T21:10:48.827+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-8ce4cb79-65f9-4654-bdc6-bc08878b06c9)
2025-09-17T21:10:48.827+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T21:10:48.827+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T21:10:48.827+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T21:10:48.827+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-8ce4cb79-65f9-4654-bdc6-bc08878b06c9 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T21:10:48.827+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T21:10:48.827+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T21:10:48.827+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T21:10:48.950+0800	INFO	rpc/rpc_client.go:337	config-0-8ce4cb79-65f9-4654-bdc6-bc08878b06c9 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758114648910_192.168.3.254_52943
2025-09-17T21:10:48.950+0800	INFO	rpc/rpc_client.go:486	config-0-8ce4cb79-65f9-4654-bdc6-bc08878b06c9 notify connected event to listeners , connectionId=1758114648910_192.168.3.254_52943
2025-09-17T21:10:48.950+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
