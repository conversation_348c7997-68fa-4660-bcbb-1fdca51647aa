package test

import (
	"brainHub/internal/service"
	"testing"

	// 導入 logic 包以確保所有服務的 init 函數被執行
	_ "brainHub/internal/logic"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestEmbeddingIntegration 測試 embedding 服務的完整集成
func TestEmbeddingIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 測試 embedding 服務是否已註冊
		embeddingService := service.Embedding()
		t.AssertNE(embeddingService, nil)

		// 測試 DSH 服務是否已註冊
		dshService := service.DSH()
		t.AssertNE(dshService, nil)

		// 測試獲取支持的提供商
		providers := embeddingService.GetSupportedProviders()
		t.AssertGT(len(providers), 0)
		t.AssertIN("aoai", providers)

		// 注意：不測試實際的 embedding 提供商獲取，因為需要真實的配置環境
		// 但上面的測試已經證明了服務註冊是成功的
	})
}
