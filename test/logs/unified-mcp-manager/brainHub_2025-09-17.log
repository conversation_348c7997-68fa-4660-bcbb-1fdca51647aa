2025-09-17T20:31:38.028+08:00 [INFO] Initializing unified MCP manager with 1 clients
2025-09-17T20:31:44.892+08:00 [ERRO] Failed to initialize client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8") 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPManager).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_manager.go:77
2.  brainHub/internal/llms/mcp.NewUnifiedMCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_integration.go:36
3.  brainHub/test.TestUnifiedMCPIntegration_SSEConnection.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:19
4.  brainHub/test.TestUnifiedMCPIntegration_SSEConnection
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:15

2025-09-17T20:31:44.892+08:00 [INFO] Unified MCP manager initialization completed: 0/1 clients successful
2025-09-17T20:31:44.892+08:00 [WARN] Some MCP clients failed to initialize: [client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8")]
2025-09-17T20:31:44.893+08:00 [DEBU] All MCP clients closed successfully
2025-09-17T20:32:00.338+08:00 [INFO] Initializing unified MCP manager with 1 clients
2025-09-17T20:32:06.856+08:00 [ERRO] Failed to initialize client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8") 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPManager).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_manager.go:77
2.  brainHub/internal/llms/mcp.NewUnifiedMCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_integration.go:36
3.  brainHub/test.TestUnifiedMCPIntegration_ToolListRetrieval.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:56
4.  brainHub/test.TestUnifiedMCPIntegration_ToolListRetrieval
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:52

2025-09-17T20:32:06.856+08:00 [INFO] Unified MCP manager initialization completed: 0/1 clients successful
2025-09-17T20:32:06.856+08:00 [WARN] Some MCP clients failed to initialize: [client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8")]
2025-09-17T20:32:06.857+08:00 [DEBU] Getting tool definitions from all MCP clients
2025-09-17T20:32:06.857+08:00 [DEBU] Total tool definitions retrieved: 0
2025-09-17T20:32:06.857+08:00 [DEBU] Getting tool definitions from all MCP clients
2025-09-17T20:32:06.857+08:00 [DEBU] Total tool definitions retrieved: 0
2025-09-17T20:32:06.857+08:00 [DEBU] All MCP clients closed successfully
2025-09-17T20:32:16.267+08:00 [INFO] Initializing unified MCP manager with 1 clients
2025-09-17T20:32:22.774+08:00 [ERRO] Failed to initialize client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8") 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPManager).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_manager.go:77
2.  brainHub/internal/llms/mcp.NewUnifiedMCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_integration.go:36
3.  brainHub/test.TestUnifiedMCPIntegration_BackwardCompatibility.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:148
4.  brainHub/test.TestUnifiedMCPIntegration_BackwardCompatibility
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:144

2025-09-17T20:32:22.774+08:00 [INFO] Unified MCP manager initialization completed: 0/1 clients successful
2025-09-17T20:32:22.774+08:00 [WARN] Some MCP clients failed to initialize: [client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8")]
2025-09-17T20:32:22.774+08:00 [DEBU] Getting tool definitions from all MCP clients
2025-09-17T20:32:22.775+08:00 [DEBU] Total tool definitions retrieved: 0
2025-09-17T20:32:22.775+08:00 [DEBU] All MCP clients closed successfully
2025-09-17T20:32:32.100+08:00 [INFO] Initializing unified MCP manager with 1 clients
2025-09-17T20:32:38.652+08:00 [ERRO] Failed to initialize client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8") 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPManager).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_manager.go:77
2.  brainHub/internal/llms/mcp.NewUnifiedMCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_integration.go:36
3.  brainHub/test.TestUnifiedMCPIntegration_PerformanceBaseline.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:227
4.  brainHub/test.TestUnifiedMCPIntegration_PerformanceBaseline
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:222

2025-09-17T20:32:38.652+08:00 [INFO] Unified MCP manager initialization completed: 0/1 clients successful
2025-09-17T20:32:38.652+08:00 [WARN] Some MCP clients failed to initialize: [client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8")]
2025-09-17T20:32:38.652+08:00 [DEBU] Getting tool definitions from all MCP clients
2025-09-17T20:32:38.652+08:00 [DEBU] Total tool definitions retrieved: 0
2025-09-17T20:32:38.652+08:00 [DEBU] All MCP clients closed successfully
2025-09-17T20:37:15.437+08:00 [INFO] Initializing unified MCP manager with 1 clients
2025-09-17T20:37:22.401+08:00 [ERRO] Failed to initialize client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8") 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPManager).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_manager.go:77
2.  brainHub/internal/llms/mcp.NewUnifiedMCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_integration.go:36
3.  brainHub/test.TestUnifiedMCPIntegration_SSEConnection.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:19
4.  brainHub/test.TestUnifiedMCPIntegration_SSEConnection
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_unified_integration_test.go:15

2025-09-17T20:37:22.401+08:00 [INFO] Unified MCP manager initialization completed: 0/1 clients successful
2025-09-17T20:37:22.401+08:00 [WARN] Some MCP clients failed to initialize: [client 'faceRecognition': MCP initialize failed: failed after 3 attempts (cause: failed to connect to MCP server: calling "initialize": sending "initialize": unsupported content type "text/event-stream; charset=utf-8")]
2025-09-17T20:37:22.401+08:00 [DEBU] All MCP clients closed successfully
2025-09-17T20:39:55.221+08:00 [INFO] Initializing unified MCP manager with 1 clients
2025-09-17T20:39:56.720+08:00 [DEBU] Successfully initialized client 'faceRecognition'
2025-09-17T20:39:56.720+08:00 [INFO] Unified MCP manager initialization completed: 1/1 clients successful
2025-09-17T20:39:56.720+08:00 [DEBU] Getting tool definitions from all MCP clients
2025-09-17T20:39:56.721+08:00 [ERRO] Failed to get tool definitions from client 'faceRecognition': failed to list tools: connection closed: calling "tools/list": client is closing: EOF 
Stack:
1.  brainHub/internal/llms/mcp.(*UnifiedMCPManager).GetToolDefinitions
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/mcp/unified_manager.go:124
2.  brainHub/test.TestMCPSSEConnection_WithProvidedConfig.func1
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_sse_connection_test.go:65
3.  brainHub/test.TestMCPSSEConnection_WithProvidedConfig
    /Users/<USER>/Source/Ai app/brainHub/test/mcp_sse_connection_test.go:15

2025-09-17T20:39:56.721+08:00 [DEBU] Total tool definitions retrieved: 0
2025-09-17T20:39:56.721+08:00 [DEBU] Successfully closed client 'faceRecognition'
2025-09-17T20:39:56.721+08:00 [DEBU] All MCP clients closed successfully
