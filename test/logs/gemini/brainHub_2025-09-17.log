2025-09-17T16:06:03.030+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T16:06:03.030+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:96
3.  brainHub/test.TestGemini_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:78

2025-09-17T16:06:03.031+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T16:06:03.031+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:96
3.  brainHub/test.TestGemini_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:78

2025-09-17T16:12:11.898+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T16:12:11.899+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:96
3.  brainHub/test.TestGemini_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:78

2025-09-17T16:12:11.901+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T16:12:11.901+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:96
3.  brainHub/test.TestGemini_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:78

2025-09-17T16:14:53.081+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T16:14:53.081+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:96
3.  brainHub/test.TestGemini_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:78

2025-09-17T16:14:53.082+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T16:14:53.082+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:96
3.  brainHub/test.TestGemini_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:78

2025-09-17T17:05:03.664+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T17:05:03.664+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:96
3.  brainHub/test.TestGemini_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:78

2025-09-17T17:05:03.665+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T17:05:03.665+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:96
3.  brainHub/test.TestGemini_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:78

2025-09-17T18:28:18.773+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T18:28:18.773+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_InitializationWithMCP.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:96
3.  brainHub/test.TestGemini_InitializationWithMCP
    /Users/<USER>/Source/Ai app/brainHub/test/llm_initialization_test.go:78

2025-09-17T18:28:18.774+08:00 [DEBU] initiliaze with parameter  : {
	"aoai": null,
	"common": {
		"max_output_tokens": 0,
		"temperature": 0
	},
	"vertex": {
		"credential_file": "",
		"gemini": {
			"include_thoughts": false,
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0,
			"thinking_budget": 0
		},
		"llm_type": "",
		"project_id": "",
		"region": "",
		"third_model": {
			"api_version": "",
			"max_output_tokens": 0,
			"model": "",
			"temperature": 0
		}
	}
}
2025-09-17T18:28:18.774+08:00 [ERRO] the credential file is not exist 
Stack:
1.  brainHub/internal/llms/gemini.(*GeminiLLM).Initialize
    /Users/<USER>/Source/Ai app/brainHub/internal/llms/gemini/genai.go:86
2.  brainHub/test.TestGemini_MCPIntegration.func1
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:96
3.  brainHub/test.TestGemini_MCPIntegration
    /Users/<USER>/Source/Ai app/brainHub/test/llm_mcp_integration_test.go:78

