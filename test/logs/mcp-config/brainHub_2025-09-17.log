2025-09-17T16:06:01.910+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:06:01.911+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:06:01.911+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:06:01.911+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:06:01.911+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:06:01.911+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:06:02.977+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:06:02.977+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:06:02.977+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:06:02.977+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:06:02.977+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:06:02.977+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:06:02.977+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:06:02.977+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:06:02.977+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:06:02.977+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:06:02.977+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:06:02.978+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:06:02.978+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:06:02.978+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:06:02.978+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:06:02.978+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:06:02.978+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:06:02.978+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:06:03.031+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:06:03.031+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:06:03.031+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:10:56.148+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:10:56.149+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:10:56.149+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:10:56.149+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:10:56.149+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:10:56.149+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:11:42.586+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:11:42.586+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:11:42.586+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:11:42.587+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:11:42.587+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:11:42.587+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:12:10.800+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:12:10.801+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:12:10.801+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:12:10.801+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:12:10.801+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:12:10.801+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:12:11.842+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:12:11.842+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:12:11.842+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:12:11.842+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:12:11.844+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:12:11.844+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:12:11.844+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:12:11.844+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:12:11.844+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:12:11.844+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:12:11.844+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:12:11.844+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:12:11.844+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:12:11.844+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:12:11.844+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:12:11.844+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:12:11.844+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:12:11.844+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:12:11.901+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:12:11.901+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:12:11.901+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:14:51.536+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:14:51.536+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:14:51.536+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:14:51.537+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:14:51.537+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:14:51.537+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:14:53.026+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:14:53.026+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:14:53.026+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:14:53.027+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:14:53.027+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:14:53.027+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:14:53.027+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:14:53.027+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:14:53.027+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:14:53.027+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:14:53.027+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:14:53.027+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:14:53.027+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:14:53.027+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:14:53.027+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:14:53.027+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:14:53.027+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:14:53.027+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T16:14:53.082+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T16:14:53.082+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T16:14:53.082+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T17:05:02.562+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T17:05:02.562+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T17:05:02.562+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T17:05:02.562+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T17:05:02.562+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T17:05:02.562+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T17:05:03.610+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T17:05:03.610+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T17:05:03.610+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T17:05:03.610+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T17:05:03.610+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T17:05:03.610+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T17:05:03.610+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T17:05:03.610+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T17:05:03.610+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T17:05:03.610+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T17:05:03.610+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T17:05:03.610+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T17:05:03.610+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T17:05:03.610+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T17:05:03.610+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T17:05:03.610+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T17:05:03.610+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T17:05:03.610+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T17:05:03.665+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T17:05:03.665+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T17:05:03.665+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T18:28:17.398+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T18:28:17.398+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T18:28:17.398+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T18:28:17.398+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T18:28:17.398+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T18:28:17.398+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T18:28:18.771+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T18:28:18.771+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T18:28:18.771+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
2025-09-17T18:28:18.774+08:00 [DEBU] Loading MCP configuration from config files
2025-09-17T18:28:18.774+08:00 [DEBU] Found MCP config at path: mcp_config
2025-09-17T18:28:18.774+08:00 [DEBU] MCP configuration loaded successfully: enabled=true, servers=0
