# MCP 配置範例與遷移指南

## 完整配置範例

### 1. 基本配置範例

```yaml
# manifest/config/config.yaml
mcp_config:
  enabled: true
  global:
    timeout: 30
    environment:
      MCP_DEBUG: "false"
      GLOBAL_API_KEY: "${API_KEY}"
  
  servers:
    # STDIO 傳輸範例
    - name: "filesystem"
      type: "stdio"
      command: "mcp-server-filesystem"
      args: ["--root", "/workspace", "--readonly"]
      timeout: 60
      environment:
        PATH: "/usr/local/bin:/usr/bin:/bin"
        HOME: "${HOME}"
    
    # SSE 傳輸範例
    - name: "weather-service"
      type: "sse"
      url: "https://weather-mcp.example.com/sse"
      environment:
        WEATHER_API_KEY: "${WEATHER_API_KEY}"
    
    # Streamable 傳輸範例（推薦）
    - name: "database-tools"
      type: "streamable"
      url: "https://db-mcp.example.com/streamable"
      timeout: 45
      environment:
        DB_CONNECTION_STRING: "${DATABASE_URL}"
        DB_POOL_SIZE: "10"
```

### 2. 環境變數展開範例

```yaml
mcp_config:
  enabled: true
  global:
    environment:
      # 全域環境變數
      BASE_URL: "${MCP_BASE_URL:-https://localhost:8080}"
  
  servers:
    - name: "dynamic-server"
      type: "streamable"
      url: "${MCP_SERVER_URL}/api/mcp"  # 從環境變數展開
      environment:
        # 伺服器特定環境變數
        SERVER_NAME: "${HOSTNAME}"
        CONFIG_PATH: "${HOME}/.config/mcp"
        API_TOKEN: "${MCP_API_TOKEN}"
        # 預設值語法
        TIMEOUT: "${MCP_TIMEOUT:-30}"
        DEBUG_MODE: "${DEBUG:-false}"
```

### 3. 多伺服器混合配置

```yaml
mcp_config:
  enabled: true
  global:
    timeout: 30
    environment:
      COMMON_SECRET: "${SHARED_SECRET}"
  
  servers:
    # 本地文件系統工具
    - name: "local-fs"
      type: "stdio"
      command: "/usr/local/bin/mcp-filesystem"
      args: ["--workspace", "${WORKSPACE_PATH}"]
      
    # 遠端 API 服務
    - name: "api-gateway"
      type: "streamable"
      url: "${API_GATEWAY_URL}/mcp"
      timeout: 60
      environment:
        API_VERSION: "v2"
        RATE_LIMIT: "1000"
    
    # 即時通訊服務
    - name: "chat-service"
      type: "sse"
      url: "${CHAT_SERVICE_URL}/events"
      environment:
        ROOM_ID: "${CHAT_ROOM_ID}"
        USER_TOKEN: "${USER_AUTH_TOKEN}"
    
    # 資料庫查詢工具
    - name: "db-query"
      type: "stdio"
      command: "python"
      args: ["-m", "mcp_db_server", "--config", "${DB_CONFIG_PATH}"]
      environment:
        PYTHONPATH: "${PROJECT_ROOT}/tools"
        DB_DRIVER: "postgresql"
```

## 遷移指南

### 從 HTTP 傳輸遷移到 Streamable

#### 1. 識別需要遷移的配置

**舊版 HTTP 配置：**
```yaml
servers:
  - name: "legacy-service"
    type: "http"  # 已棄用
    url: "https://api.example.com/mcp"
    headers:      # 不再支援
      Authorization: "Bearer ${API_TOKEN}"
    retry_count: 3  # 已移至 global 配置
```

**遷移後的 Streamable 配置：**
```yaml
servers:
  - name: "legacy-service"
    type: "streamable"  # 更新傳輸類型
    url: "https://api.example.com/mcp"
    environment:
      # 認證資訊移至環境變數
      API_TOKEN: "${API_TOKEN}"
```

#### 2. 欄位對應表

| 舊版 HTTP 欄位 | 新版對應 | 說明 |
|---------------|---------|------|
| `type: "http"` | `type: "streamable"` | 傳輸類型更新 |
| `headers` | `environment` | 認證資訊移至環境變數 |
| `timeout` | `timeout` | 保持不變 |
| `url` | `url` | 保持不變 |

#### 3. 遷移步驟

1. **備份現有配置**
   ```bash
   cp manifest/config/config.yaml manifest/config/config.yaml.backup
   ```

2. **更新配置檔案**
   - 將 `type: "http"` 改為 `type: "streamable"`
   - 移除 `headers` 欄位，將認證資訊移至 `environment`

3. **驗證伺服器相容性**
   ```bash
   # 測試新的 streamable 端點
   curl -X POST https://api.example.com/mcp \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc":"2.0","method":"initialize","params":{},"id":1}'
   ```

4. **測試配置**
   ```bash
   # 重啟服務並檢查日誌
   ./main
   ```

#### 4. 常見遷移問題

**問題 1：認證失敗**
```
Error: authentication failed for server 'legacy-service'
```

**解決方案：**
確保環境變數正確設定，並在伺服器端實現認證邏輯：
```yaml
environment:
  API_TOKEN: "${API_TOKEN}"
  # 確保環境變數已設定
```

**問題 2：端點不支援 streamable**
```
Error: server does not support streamable transport
```

**解決方案：**
1. 檢查伺服器文檔確認支援的傳輸類型
2. 如果不支援，考慮使用 SSE 傳輸：
```yaml
type: "sse"
url: "https://api.example.com/sse"
```

**問題 3：連線超時**
```
Error: connection timeout after 30s
```

**解決方案：**
調整超時設定：
```yaml
global:
  timeout: 60  # 增加全域超時
servers:
  - name: "slow-service"
    timeout: 120  # 或針對特定伺服器調整
```

### 遷移檢查清單

- [ ] 備份現有配置檔案
- [ ] 更新所有 `type: "http"` 為 `type: "streamable"`
- [ ] 移除 `headers` 欄位，改用 `environment`
- [ ] 設定必要的環境變數
- [ ] 測試各個伺服器連線
- [ ] 檢查應用程式日誌確認無錯誤
- [ ] 驗證 MCP 工具功能正常

### 自動遷移工具

系統提供內建的遷移分析工具：

```go
// 使用遷移工具分析配置
migrationTool := mcp.NewConfigMigrationTool()
suggestions, err := migrationTool.AnalyzeConfiguration(ctx)
if err != nil {
    log.Fatal(err)
}

// 生成遷移報告
report, err := migrationTool.GenerateMigrationReport(ctx)
if err != nil {
    log.Fatal(err)
}
fmt.Println(report)
```

遷移工具會自動：
- 識別使用 HTTP 傳輸的伺服器
- 生成對應的 Streamable 配置範例
- 提供詳細的遷移步驟說明
- 驗證遷移後的配置正確性
