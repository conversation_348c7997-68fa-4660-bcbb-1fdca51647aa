# brainHub - AI 聊天與文件處理後端服務

[![Go Version](https://img.shields.io/badge/Go-1.23-blue.svg)](https://golang.org/)
[![GoFrame](https://img.shields.io/badge/GoFrame-v2.9.0-green.svg)](https://goframe.org/)
[![MCP SDK](https://img.shields.io/badge/MCP_SDK-v0.5.1+-orange.svg)](https://github.com/modelcontextprotocol/go-sdk)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 📚 MCP 文檔索引

本目錄包含 brainHub 項目中 MCP (Model Context Protocol) 模組的完整文檔。

### 🚀 快速開始
- **[MCP 配置指南](mcp-configuration-guide.md)** - 詳細的配置結構說明與驗證規則
- **[配置範例與遷移指南](mcp-configuration-examples.md)** - 完整的 YAML 配置範例與 HTTP 到 Streamable 遷移指南
- **[API 使用指南](mcp-api-usage.md)** - API 使用範例與最佳實踐

### 📖 詳細文檔（舊版，供參考）
- **[MCP 整合指南](MCP_Integration_Guide.md)** - 系統整合說明
- **[MCP API 參考](MCP_API_Reference.md)** - API 接口文檔
- **[MCP 配置架構](MCP_Configuration_Architecture.md)** - 配置系統架構
- **[MCP 部署指南](MCP_Deployment_Guide.md)** - 部署與運維指南
- **[MCP 遷移指南](MCP_Migration_Guide.md)** - 版本遷移說明
- **[MCP 效能優化](MCP_Performance_Optimization.md)** - 效能調優指南
- **[MCP 知識轉移](MCP_Knowledge_Transfer.md)** - 技術知識文檔

### 🔧 配置範例
- **[mcp_config_examples.yaml](mcp_config_examples.yaml)** - 配置檔案範例

## 🆕 v2 MCP 模組特性

### 核心改進
- ✅ **官方 SDK 整合**: 基於 [modelcontextprotocol/go-sdk](https://github.com/modelcontextprotocol/go-sdk) v0.5.1+
- ✅ **多傳輸協議支援**: STDIO、SSE、Streamable（推薦）
- ✅ **環境變數展開**: 支援 `${VAR}` 語法
- ✅ **全域配置合併**: 自動套用全域設定到各伺服器
- ✅ **配置驗證**: 完整的配置驗證與錯誤提示
- ✅ **遷移工具**: 內建 HTTP 到 Streamable 遷移分析
- ✅ **配置緩存**: 5 分鐘配置緩存提升效能

### 支援的傳輸協議

| 協議 | 狀態 | 使用場景 | 必要參數 |
|------|------|----------|----------|
| `stdio` | ✅ 支援 | 本地程序通訊 | `command` |
| `sse` | ✅ 支援 | 單向資料流 | `url` |
| `streamable` | ✅ 推薦 | 雙向串流通訊 | `url` |
| `http` | ❌ 已棄用 | - | - |

## 🔄 從舊版遷移

如果您正在使用舊版 MCP 配置，請參考：

1. **[配置範例與遷移指南](mcp-configuration-examples.md#遷移指南)** - 詳細的遷移步驟
2. **內建遷移工具** - 自動分析並生成遷移建議

```go
migrationTool := mcp.NewConfigMigrationTool()
report, err := migrationTool.GenerateMigrationReport(ctx)
```

## 📋 快速配置檢查清單

### 基本配置
- [ ] 設定 `enabled: true`
- [ ] 配置至少一個伺服器
- [ ] 為每個伺服器指定唯一的 `name`
- [ ] 選擇適當的 `type`（推薦 `streamable`）

### 安全性
- [ ] 使用環境變數管理敏感資訊
- [ ] 避免在配置檔案中硬編碼密鑰
- [ ] 設定適當的超時值

### 效能優化
- [ ] 使用全域配置減少重複設定
- [ ] 根據服務特性調整超時設定
- [ ] 啟用配置緩存

## 🛠️ 開發工具

### 配置驗證
```bash
# 驗證配置檔案
go run main.go --validate-mcp-config
```

### 遷移分析
```bash
# 分析配置並生成遷移建議
go run main.go --analyze-mcp-migration
```

### 連線測試
```bash
# 測試 MCP 伺服器連線
go run main.go --test-mcp-connections
```

## 📞 支援與回饋

如果您在使用 MCP 模組時遇到問題：

1. 查看 **[API 使用指南](mcp-api-usage.md#常見問題與解決方案)** 中的常見問題
2. 檢查應用程式日誌中的詳細錯誤訊息
3. 使用內建的遷移工具分析配置問題
4. 參考配置範例確保格式正確

## 🔗 相關資源

- [Model Context Protocol 官方規範](https://modelcontextprotocol.io/)
- [MCP Go SDK 文檔](https://github.com/modelcontextprotocol/go-sdk)
- [GoFrame 框架文檔](https://goframe.org/)

---

**最後更新**: 2025-09-17
**MCP 模組版本**: v2.0
**Go SDK 版本**: v0.5.1+

---

# brainHub 項目概述

## 📋 專案概述

brainHub 是一個基於 GoFrame 框架構建的 AI 聊天和文件處理後端服務，專為現代 AI 應用設計。它整合了多種大語言模型（LLM）和 Model Context Protocol (MCP) 支援，提供強大的 AI 聊天功能和文件處理能力。

### 🎯 目標用戶群體
- **AI 應用開發者**：需要整合多種 LLM 和工具的開發團隊
- **企業開發團隊**：構建智能聊天和文件處理系統的組織
- **系統集成商**：提供 AI 解決方案的服務商
- **研究人員**：需要靈活 AI 工具整合的研究團隊

### 🚀 主要使用場景
- **AI 聊天服務**：支援多種 LLM 的智能對話系統
- **文件處理**：AI 驅動的文件上傳、分析和處理
- **工具整合**：透過 MCP 協議整合各種外部工具
- **全管道聊天**：統一的多管道聊天體驗

## ✨ 主要特性

### 🤖 AI 聊天能力
- **多 LLM 支援**：Google Vertex AI (Gemini)、Azure OpenAI、Claude
- **智能路由**：自動選擇最適合的 LLM 處理請求
- **附件處理**：支援文件上傳與 AI 分析
- **串流回應**：即時的對話體驗

### 🔧 MCP 工具整合
- **官方 SDK**：基於 modelcontextprotocol/go-sdk v0.5.1+
- **多傳輸協議**：STDIO、SSE、Streamable 支援
- **動態工具載入**：靈活的外部工具整合
- **配置管理**：環境變數展開與全域配置合併

### 🏗️ 企業級架構
- **微服務設計**：模組化的服務架構
- **RESTful API**：完整的 HTTP API 接口
- **容器化部署**：Docker 和 Kubernetes 支持
- **高可用性**：支持集群部署和負載均衡

## 🏛️ 技術架構

### 整體架構設計

```mermaid
graph TB
    subgraph "API 層"
        A[Execute API] 
        B[Query API]
        C[Vector API]
    end
    
    subgraph "控制器層"
        D[HTTP Controllers]
    end
    
    subgraph "服務層"
        E[Service Interfaces]
    end
    
    subgraph "邏輯層"
        F[MariaDB Logic]
        G[Vector Logic] 
        H[MessageQ Logic]
    end
    
    subgraph "數據層"
        I[(MariaDB)]
        J[(Weaviate)]
        K[RabbitMQ]
    end
    
    subgraph "外部服務"
        L[Nacos]
        M[Azure OpenAI]
        N[Google AI]
    end
    
    A --> D
    B --> D  
    C --> D
    D --> E
    E --> F
    E --> G
    E --> H
    F --> I
    G --> J
    H --> K
    L --> E
    M --> G
    N --> G
```

### 目錄結構說明

```
dataSyncHub/
├── api/                    # API 定義層
│   ├── executeapi/         # SQL 執行相關 API
│   ├── queryapi/           # 數據查詢相關 API  
│   └── vector/             # 向量數據庫操作 API
├── boot/                   # 應用程序初始化和啟動配置
├── docs/                   # 專案文檔
├── hack/                   # 開發工具和腳本
├── internal/               # 內部應用程序代碼
│   ├── cmd/                # 命令行入口和路由配置
│   ├── consts/             # 常量定義
│   ├── controller/         # HTTP 控制器實現
│   ├── dao/                # 數據訪問對象（自動生成）
│   ├── logic/              # 業務邏輯實現
│   │   ├── mariadb/        # MariaDB 相關邏輯
│   │   ├── messageQ/       # 消息隊列處理邏輯
│   │   ├── settings/       # 設置管理邏輯
│   │   └── vector/         # 向量數據庫邏輯
│   ├── model/              # 數據模型定義
│   ├── packed/             # 打包資源
│   └── service/            # 服務接口定義
├── logs/                   # 日誌文件目錄
├── manifest/               # 部署清單
│   ├── config/             # 配置文件模板
│   ├── deploy/             # Kubernetes 部署文件
│   └── docker/             # Docker 相關文件
├── nacos/                  # Nacos 配置文件
└── main.go                 # 應用程序入口點
```

### 核心組件關係

1. **消息驅動架構**：RabbitMQ 接收消息 → 路由到對應處理器 → MariaDB/Weaviate 操作
2. **RESTful API 架構**：HTTP 請求 → 控制器 → 服務層 → 邏輯層 → 數據層
3. **配置管理**：Nacos 集中配置 → 本地配置文件 → 環境變量
4. **服務發現**：Nacos 註冊中心 → 微服務發現 → 負載均衡

## 🛠️ 技術棧詳細說明

### 核心技術
- **程式語言**：Go 1.24
- **Web 框架**：GoFrame v2.9.0
- **架構模式**：微服務架構 + 消息驅動架構

### 數據存儲
- **關係型數據庫**：MariaDB/MySQL
- **向量數據庫**：Weaviate v1.30.6
- **消息隊列**：RabbitMQ AMQP v1.10.0

### 服務治理
- **服務發現**：Nacos v2.2.7
- **配置管理**：Nacos 配置中心
- **服務註冊**：自動服務註冊和發現

### AI 和機器學習
- **向量嵌入**：Azure OpenAI、Google AI
- **嵌入模型**：text-embedding-ada-002
- **向量操作**：Weaviate Go Client v5.1.0

### 容器化和部署
- **容器化**：Docker
- **編排**：Kubernetes
- **配置管理**：Kustomize

### 開發工具
- **代碼生成**：GoFrame CLI 工具
- **構建工具**：Make
- **依賴管理**：Go Modules

## 🚀 快速開始

### 環境需求
- Go 1.18 或更高版本
- Docker 和 Docker Compose
- MariaDB/MySQL 數據庫實例
- Weaviate 向量數據庫實例
- RabbitMQ 消息隊列實例
- Nacos 服務註冊中心

### 快速安裝

1. **克隆專案**
```bash
git clone <repository-url>
cd dataSyncHub
```

2. **安裝依賴**
```bash
go mod download
```

3. **安裝 GoFrame CLI 工具**
```bash
make cli
```

4. **構建專案**
```bash
make build
```

5. **運行專案**
```bash
./main
# 或者
go run main.go
```

### Docker 快速部署

```bash
# 構建 Docker 鏡像
make image

# 運行容器
docker run -p 8000:8000 dataSyncHub
```

## 📖 詳細安裝指南

### 1. 環境準備

#### Go 環境安裝
```bash
# 下載並安裝 Go 1.24
wget https://golang.org/dl/go1.24.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.24.linux-amd64.tar.gz
export PATH=$PATH:/usr/local/go/bin
```

#### Docker 環境安裝
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install docker.io docker-compose

# CentOS/RHEL
sudo yum install docker docker-compose
```

### 2. 數據庫設置

#### MariaDB/MySQL 設置
```sql
-- 創建數據庫
CREATE DATABASE dsh CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 創建用戶並授權
CREATE USER 'dsh_user'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON dsh.* TO 'dsh_user'@'%';
FLUSH PRIVILEGES;
```

#### Weaviate 設置
```bash
# 使用 Docker 運行 Weaviate
docker run -d \
  --name weaviate \
  -p 8080:8080 \
  -e QUERY_DEFAULTS_LIMIT=25 \
  -e AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true \
  -e PERSISTENCE_DATA_PATH='/var/lib/weaviate' \
  semitechnologies/weaviate:latest
```

#### RabbitMQ 設置
```bash
# 使用 Docker 運行 RabbitMQ
docker run -d \
  --name rabbitmq \
  -p 5672:5672 \
  -p 15672:15672 \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=admin \
  rabbitmq:3-management
```

### 3. Nacos 設置

#### 安裝和啟動 Nacos
```bash
# 下載並運行 Nacos
wget https://github.com/alibaba/nacos/releases/download/2.2.0/nacos-server-2.2.0.tar.gz
tar -xzf nacos-server-2.2.0.tar.gz
cd nacos/bin
./startup.sh -m standalone
```

#### 配置 Nacos 配置中心
1. **訪問 Nacos 控制台**：http://localhost:8848/nacos
2. **登錄**：用戶名/密碼：nacos/nacos
3. **創建配置**：
   - **Data ID**：`dsh.yaml`
   - **Group**：`DEFAULT_GROUP`
   - **配置格式**：YAML
   - **配置內容**：參考上面的 Nacos 配置中心業務配置

#### 配置內容範例
在 Nacos 控制台中創建配置，內容如下：

```yaml
server:
  address: ":8087"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"

logger:
  level: "all"
  stdout: true
  path: "./logs"
  file: "dsh_{Y-m-d}.log"

database:
  default:
    link: "mysql:root:your_password@tcp(127.0.0.1:3306)?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true
  dsh:
    link: "mysql:root:your_password@tcp(127.0.0.1:3306)/dsh?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true

rabbitMQ:
  url: "amqp://admin:admin@127.0.0.1:5672/"

vectorEmbeddings:
  embedding:
    provider: azure
    azure:
      resourceName: "your-resource-name"
      deploymentId: "text-embedding-ada-002"
      api_key: "your-api-key"

weaviate:
  host: "localhost:8080"
  scheme: "http"
```

### 4. 配置文件設置

#### 本地 Nacos 連接配置

創建本地配置文件 `manifest/config/config.yaml`（僅用於連接 Nacos）：

```yaml
# 本地配置文件，主要用於連接 Nacos 配置中心
nacos:
  mode: "dev"  # 或 "pro" 用於生產環境
  dev:
    ip: "127.0.0.1"
    port: 8848
    nameSpace: "public"
    userName: "nacos"
    password: "nacos"
    # 服務註冊配置
    reg:
      serviceName: "dataSyncHub"
      serviceGroup: "DEFAULT_GROUP"
      clusterName: "DEFAULT"
      catchDir: "./nacos/cache"
      logDir: "./nacos/logs"
    # 配置中心配置
    cfg:
      dataId: "dsh.yaml"
      serviceGroup: "DEFAULT_GROUP"
      catchDir: "./nacos/config"
      logDir: "./nacos/logs"
  # 日誌配置
  log:
    maxSize: 10
    maxAge: 3
    maxBackup: 5
    localTime: true
    compress: true
```

#### Nacos 配置中心設置

在 Nacos 配置中心創建配置文件（DataId: `dsh.yaml`, Group: `DEFAULT_GROUP`）：

```yaml
# Nacos 配置中心中的完整業務配置
server:
  address: ":8000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"

logger:
  level: "all"
  stdout: true
  path: "./logs"
  file: "dsh_{Y-m-d}.log"
  rotateExpire: "1d"
  rotateBackupLimit: 1
  rotateBackupExpire: "7d"
  rotateBackupCompress: 9

database:
  default:
    link: "mysql:root:your_password@tcp(127.0.0.1:3306)?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true
  dsh:
    link: "mysql:root:your_password@tcp(127.0.0.1:3306)/dsh?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true
  logger:
    path: "logs/db"
    level: "all"
    stdout: false
    rotateExpire: "1d"
    rotateBackupLimit: 1
    rotateBackupExpire: "7d"
    rotateBackupCompress: 9

rabbitMQ:
  url: "amqp://admin:admin@127.0.0.1:5672/"

vectorEmbeddings:
  embedding:
    provider: azure  # 或 google
    azure:
      resourceName: "your-resource-name"
      deploymentId: "text-embedding-ada-002"
      api_key: "your-api-key"
    google:
      projectId: "your-project-id"
      modelId: "text-embedding-004"
      studioAPIKey: "your-studio-api-key"

weaviate:
  host: "localhost:8080"
  scheme: "http"
```

## 📚 API 文檔

### 執行 API (executeapi)

#### 執行 SQL 語句
```http
POST /executeapi/v1/executeSQL
Content-Type: application/json

{
  "schema": "dsh",
  "raw_sql": "SELECT * FROM users WHERE id = 1"
}
```

**回應範例**：
```json
{
  "code": 0,
  "message": "success",
  "cost": "10ms"
}
```

#### 批量執行 SQL 語句
```http
POST /executeapi/v1/batchExecuteSQL
Content-Type: application/json

{
  "schema": "dsh",
  "sql_list": [
    "INSERT INTO users (name, email) VALUES ('張三', '<EMAIL>')",
    "INSERT INTO users (name, email) VALUES ('李四', '<EMAIL>')"
  ]
}
```

**回應範例**：
```json
{
  "code": 0,
  "message": "success",
  "cost": "25ms",
  "total_count": 2,
  "success_count": 2,
  "fail_count": 0,
  "errors": []
}
```

### 查詢 API (queryapi)

#### 數據查詢
```http
POST /queryapi/v1/getContents
Content-Type: application/json

{
  "table": "users",
  "conditions": {
    "name": "張三"
  },
  "limit": 10,
  "offset": 0
}
```

### 向量 API (vector)

#### 創建向量集合
```http
POST /vector/v1/createCollection
Content-Type: application/json

{
  "collection": "documents",
  "properties": [
    {
      "name": "title",
      "dataType": "text"
    },
    {
      "name": "content",
      "dataType": "text"
    }
  ],
  "vectorizer": "text2vec-openai"
}
```

#### 創建向量數據
```http
POST /vector/v1/createData
Content-Type: application/json

{
  "collection": "documents",
  "properties": {
    "title": "技術文檔",
    "content": "這是一份關於 AI 技術的詳細文檔..."
  }
}
```

#### 相似性搜索
```http
POST /vector/v1/similaritySearch
Content-Type: application/json

{
  "collection": "documents",
  "query": "AI 技術文檔",
  "limit": 5,
  "certainty": 0.7
}
```

#### 混合搜索
```http
POST /vector/v1/hybridSearch
Content-Type: application/json

{
  "collection": "documents",
  "query": "技術文檔",
  "vector": [0.1, 0.2, 0.3, ...],
  "alpha": 0.5,
  "limit": 10
}
```

## ⚙️ 配置說明

### 配置架構說明

DataSyncHub 採用 **Nacos 配置中心** 進行集中配置管理：

1. **本地配置文件**（`manifest/config/config.yaml`）：僅用於連接 Nacos 配置中心
2. **Nacos 配置中心**：存儲所有業務配置，支持動態更新和多環境管理

### 本地 Nacos 連接配置

#### Nacos 服務發現配置
- `nacos.mode`：運行模式（dev/pro）
- `nacos.dev.ip`：Nacos 服務器 IP 地址
- `nacos.dev.port`：Nacos 服務器端口
- `nacos.dev.nameSpace`：命名空間 ID
- `nacos.dev.userName`：Nacos 用戶名
- `nacos.dev.password`：Nacos 密碼

#### 服務註冊配置
- `nacos.dev.reg.serviceName`：服務名稱
- `nacos.dev.reg.serviceGroup`：服務分組
- `nacos.dev.reg.clusterName`：集群名稱
- `nacos.dev.reg.catchDir`：本地緩存目錄
- `nacos.dev.reg.logDir`：日誌目錄

#### 配置中心配置
- `nacos.dev.cfg.dataId`：配置文件 ID（如：dsh.yaml）
- `nacos.dev.cfg.serviceGroup`：配置分組
- `nacos.dev.cfg.catchDir`：配置緩存目錄
- `nacos.dev.cfg.logDir`：配置日誌目錄

### Nacos 配置中心業務配置

以下配置存儲在 Nacos 配置中心，支持動態更新：

#### 服務器配置
- `server.address`：服務監聽地址，默認 `:8000`
- `server.openapiPath`：OpenAPI 文檔路徑 `/api.json`
- `server.swaggerPath`：Swagger UI 路徑 `/swagger`

#### 數據庫配置
- `database.default.link`：默認數據庫連接字符串
- `database.dsh.link`：業務數據庫連接字符串
- `database.default.debug`：是否開啟數據庫調試模式
- `database.logger.*`：數據庫日誌配置

#### 消息隊列配置
- `rabbitMQ.url`：RabbitMQ 連接 URL

#### 向量嵌入配置
- `vectorEmbeddings.embedding.provider`：嵌入提供商（azure/google）
- `vectorEmbeddings.embedding.azure.resourceName`：Azure 資源名稱
- `vectorEmbeddings.embedding.azure.deploymentId`：Azure 部署 ID
- `vectorEmbeddings.embedding.azure.api_key`：Azure API 密鑰
- `vectorEmbeddings.embedding.google.projectId`：Google 專案 ID
- `vectorEmbeddings.embedding.google.modelId`：Google 模型 ID
- `vectorEmbeddings.embedding.google.studioAPIKey`：Google Studio API 密鑰

#### Weaviate 配置
- `weaviate.host`：Weaviate 服務地址
- `weaviate.scheme`：連接協議（http/https）

#### 日誌配置
- `logger.level`：日誌級別（all/debug/info/warn/error）
- `logger.stdout`：是否輸出到控制台
- `logger.path`：日誌文件路徑
- `logger.file`：日誌文件名格式
- `logger.rotateExpire`：日誌輪轉週期
- `logger.rotateBackupLimit`：保留日誌文件數量
- `logger.rotateBackupExpire`：日誌文件保留時間
- `logger.rotateBackupCompress`：日誌壓縮級別

### 多環境配置管理

#### 開發環境配置
```yaml
# 本地配置文件
nacos:
  mode: "dev"
  dev:
    ip: "127.0.0.1"
    port: 8848
    # ... 其他開發環境配置
```

#### 生產環境配置
```yaml
# 本地配置文件
nacos:
  mode: "pro"
  pro:
    ip: "nacos-server.production.com"
    port: 8848
    # ... 其他生產環境配置
```

### 配置熱更新

Nacos 配置中心支持配置熱更新，當配置發生變化時：
1. Nacos 會自動推送配置變更通知
2. 應用程序會自動重新加載配置
3. 無需重啟服務即可生效新配置

### 配置安全

#### 敏感信息處理
- API 密鑰等敏感信息建議使用環境變量
- 生產環境密碼應使用 Nacos 的加密配置功能
- 定期輪換 API 密鑰和數據庫密碼

## 🔧 開發指南

### 本地開發環境設置

1. **安裝 GoFrame CLI 工具**
```bash
make cli
```

2. **設置 Nacos 配置**
```bash
# 確保 Nacos 服務正在運行
# 訪問 http://localhost:8848/nacos 配置業務參數
```

3. **配置本地連接文件**
創建或修改 `manifest/config/config.yaml`：
```yaml
nacos:
  mode: "dev"
  dev:
    ip: "127.0.0.1"
    port: 8848
    nameSpace: "public"
    userName: "nacos"
    password: "nacos"
    reg:
      serviceName: "dataSyncHub"
      serviceGroup: "DEFAULT_GROUP"
    cfg:
      dataId: "dsh.yaml"
      serviceGroup: "DEFAULT_GROUP"
```

4. **生成代碼**
```bash
# 生成控制器代碼
make ctrl

# 生成數據訪問層代碼
make dao

# 生成服務接口代碼
make service
```

5. **運行開發服務器**
```bash
go run main.go
```

### 配置管理最佳實踐

#### 開發環境配置
1. **本地 Nacos 實例**：使用本地 Nacos 進行開發
2. **配置隔離**：使用不同的 namespace 隔離不同環境
3. **配置版本管理**：在 Nacos 中為配置創建版本標記

#### 配置更新流程
1. **修改配置**：在 Nacos 控制台修改配置
2. **發布配置**：點擊發布使配置生效
3. **驗證更新**：檢查應用日誌確認配置已更新
4. **回滾機制**：如有問題可快速回滾到上一版本

#### 環境配置管理
```bash
# 開發環境
nacos.mode = "dev"
# 對應 Nacos 中的開發環境配置

# 測試環境
nacos.mode = "test"
# 對應 Nacos 中的測試環境配置

# 生產環境
nacos.mode = "pro"
# 對應 Nacos 中的生產環境配置
```

### 代碼規範和最佳實踐

#### 命名規範
- **包名**：使用小寫字母，簡潔明了
- **函數名**：使用駝峰命名法，公開函數首字母大寫
- **變量名**：使用駝峰命名法，私有變量首字母小寫
- **常量名**：使用全大寫字母，單詞間用下劃線分隔

#### 代碼組織
- **API 定義**：在 `api/` 目錄下定義 RESTful API 接口
- **控制器實現**：在 `internal/controller/` 中實現 API 處理邏輯
- **業務邏輯**：在 `internal/logic/` 中實現核心業務邏輯
- **服務接口**：在 `internal/service/` 中定義服務接口

#### 錯誤處理
- 使用 GoFrame 的 `gerror` 進行錯誤處理
- 在 `internal/consts/errors.go` 中定義常見錯誤
- 使用適當的日誌級別記錄錯誤

#### 日誌記錄
使用 GoFrame 的日誌系統，包含以下類別：
- `CatHttpLogs`：HTTP 請求/響應日誌
- `CatDB`：數據庫操作日誌
- `CatMQ`：消息隊列操作日誌
- `CatWeaviate`：向量數據庫操作日誌

### 測試運行方法

#### 運行所有測試
```bash
go test ./...
```

#### 運行特定模組測試
```bash
# 測試 MariaDB 邏輯
go test ./internal/logic/mariadb/...

# 測試向量邏輯
go test ./internal/logic/vector/...

# 測試消息隊列邏輯
go test ./internal/logic/messageQ/...
```

#### 測試覆蓋率
```bash
go test -cover ./...
```

### 添加新功能

1. **定義 API**：在適當的 API 包中定義新的接口
2. **創建服務接口**：在 `internal/service/` 中定義服務接口
3. **實現業務邏輯**：在 `internal/logic/` 中實現具體邏輯
4. **創建控制器**：在 `internal/controller/` 中處理 HTTP 請求
5. **註冊路由**：在 `internal/cmd/cmd.go` 中註冊新路由
6. **編寫測試**：為新功能編寫單元測試和集成測試

## 🚀 部署指南

### Docker 部署

#### 構建 Docker 鏡像
```bash
# 構建鏡像
make image

# 構建並推送到倉庫
make image.push
```

#### 運行 Docker 容器
```bash
docker run -d \
  --name datasync-hub \
  -p 8000:8000 \
  -e DATABASE_URL="mysql://user:pass@host:3306/db" \
  -e RABBITMQ_URL="amqp://user:pass@host:5672/" \
  dataSyncHub:latest
```

### Kubernetes 部署

#### 基本部署
```bash
# 應用基本配置
kubectl apply -k manifest/deploy/kustomize/base
```

#### 開發環境部署
```bash
# 應用開發環境配置
kubectl apply -k manifest/deploy/kustomize/overlays/develop
```

#### 生產環境部署
```bash
# 應用生產環境配置
kubectl apply -k manifest/deploy/kustomize/overlays/production
```

#### 自動化部署
```bash
# 使用 Make 命令部署
make deploy TAG=v1.0.0 ENV=develop
```

### 環境變量配置

| 變量名 | 描述 | 默認值 |
|--------|------|--------|
| `SERVER_ADDRESS` | 服務監聽地址 | `:8000` |
| `DATABASE_URL` | 數據庫連接 URL | - |
| `RABBITMQ_URL` | RabbitMQ 連接 URL | - |
| `WEAVIATE_HOST` | Weaviate 服務地址 | `localhost:8080` |
| `NACOS_SERVER` | Nacos 服務器地址 | `127.0.0.1:8848` |
| `AZURE_OPENAI_KEY` | Azure OpenAI API 密鑰 | - |

## 🔍 故障排除

### 常見問題

#### 1. 服務啟動失敗
**問題**：服務無法啟動，提示連接錯誤

**解決方案**：
- 檢查數據庫連接配置
- 確認 RabbitMQ 服務是否正常運行
- 檢查 Nacos 服務是否可訪問

#### 2. 向量搜索無結果
**問題**：向量搜索返回空結果

**解決方案**：
- 檢查 Weaviate 服務狀態
- 確認向量集合是否正確創建
- 檢查嵌入模型配置

#### 3. 消息隊列處理失敗
**問題**：消息無法正常處理

**解決方案**：
- 檢查 RabbitMQ 連接狀態
- 確認消息格式是否正確
- 檢查路由鍵配置

### 日誌分析

#### 查看應用日誌
```bash
# 查看實時日誌
tail -f logs/dsh_$(date +%Y-%m-%d).log

# 查看數據庫日誌
tail -f logs/db/db_$(date +%Y-%m-%d).log
```

#### Docker 容器日誌
```bash
# 查看容器日誌
docker logs -f datasync-hub

# 查看最近 100 行日誌
docker logs --tail 100 datasync-hub
```

#### Kubernetes 日誌
```bash
# 查看 Pod 日誌
kubectl logs -f deployment/template-single

# 查看特定 Pod 日誌
kubectl logs -f pod/template-single-xxx
```

## 🤝 貢獻指南

### 如何貢獻

1. **Fork 專案**到您的 GitHub 帳戶
2. **創建功能分支**：`git checkout -b feature/amazing-feature`
3. **提交更改**：`git commit -m 'Add some amazing feature'`
4. **推送分支**：`git push origin feature/amazing-feature`
5. **創建 Pull Request**

### 代碼提交規範

使用 [Conventional Commits](https://www.conventionalcommits.org/) 規範：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**類型說明**：
- `feat`：新功能
- `fix`：錯誤修復
- `docs`：文檔更新
- `style`：代碼格式調整
- `refactor`：代碼重構
- `test`：測試相關
- `chore`：構建過程或輔助工具的變動

### 開發流程

1. **問題報告**：使用 GitHub Issues 報告 bug 或提出功能請求
2. **討論設計**：在實施前討論重大更改
3. **編寫測試**：為新功能編寫相應的測試
4. **代碼審查**：所有 PR 都需要經過代碼審查
5. **文檔更新**：更新相關文檔

## 📄 許可證

本專案採用 MIT 許可證。詳細信息請參閱 [LICENSE](LICENSE) 文件。

## 📞 聯繫方式

- **專案維護者**：[您的姓名](mailto:<EMAIL>)
- **問題報告**：[GitHub Issues](https://github.com/your-org/dataSyncHub/issues)
- **討論區**：[GitHub Discussions](https://github.com/your-org/dataSyncHub/discussions)

## 🙏 致謝

感謝以下開源專案和社區的支持：

- [GoFrame](https://goframe.org/) - 優秀的 Go Web 框架
- [Weaviate](https://weaviate.io/) - 強大的向量數據庫
- [RabbitMQ](https://www.rabbitmq.com/) - 可靠的消息隊列
- [Nacos](https://nacos.io/) - 動態服務發現和配置管理

---

**DataSyncHub** - 讓數據同步變得簡單而強大 🚀
```
