# GetAll 方法錯誤處理改進報告

## 📋 改進概述

基於對 GoFrame gdb GetAll 方法錯誤處理機制的分析，我們對 DataSyncHub 項目進行了以下改進，以更好地處理表不存在等數據庫錯誤。

## 🔍 問題分析

### 原始問題
- GoFrame 的 GetAll 方法沒有特定的 error code 來表示表不存在的錯誤
- 當表不存在時，GetAll 方法返回標準的數據庫錯誤，需要通過錯誤信息內容來判斷具體錯誤類型
- 項目缺少具體的業務錯誤碼定義

### 解決方案
通過檢查錯誤信息內容來識別表不存在的錯誤，並定義專門的錯誤常量來統一處理。

## 🛠️ 實施內容

### 1. 新增錯誤常量定義

**文件：** `internal/consts/errors.go`

```go
var (
    Success           = gcode.New(0, "success", nil)
    Failed            = gcode.New(-1, "failed", nil)
    InvalidateService = gcode.New(-2, "invalid service", nil)
    
    // 業務錯誤碼
    ErrTableNotExists = gcode.New(1001, "table does not exist", nil)
    ErrDatabaseError  = gcode.New(1002, "database error", nil)
    ErrInvalidInput   = gcode.New(1003, "invalid input", nil)
    ErrUnauthorized   = gcode.New(1004, "unauthorized", nil)
)
```

### 2. 改進 GetContents 方法錯誤處理

**文件：** `internal/logic/mariadb/mariadb.go`

#### 原始 SQL 查詢錯誤處理
```go
if r, e := db.GetAll(ctx, in.RawSQL); e != nil {
    // 檢查是否為表不存在的錯誤
    if gstr.ContainsI(e.Error(), "doesn't exist") ||
       (gstr.ContainsI(e.Error(), "Table") && gstr.ContainsI(e.Error(), "doesn't exist")) {
        s.logger().Warningf(ctx, "Table does not exist in SQL: %s, Error: %v", in.RawSQL, e)
        return nil, gerror.NewCode(consts.ErrTableNotExists, "table does not exist")
    }
    s.logger().Errorf(ctx, "Query SQL execution failed: %v, SQL: %s", e, in.RawSQL)
    return nil, gerror.NewCode(consts.ErrDatabaseError, e.Error())
}
```

#### Model 查詢錯誤處理
```go
if r, e := m.All(); e != nil {
    // 檢查是否為表不存在的錯誤
    if gstr.ContainsI(e.Error(), "doesn't exist") ||
       (gstr.ContainsI(e.Error(), "Table") && gstr.ContainsI(e.Error(), "doesn't exist")) {
        s.logger().Warningf(ctx, "Table %s does not exist, Error: %v", in.Table, e)
        return gerror.NewCode(consts.ErrTableNotExists, fmt.Sprintf("table %s does not exist", in.Table))
    }
    return gerror.NewCode(consts.ErrDatabaseError, e.Error())
}
```

### 3. 更新控制器層錯誤處理

**文件：** `internal/controller/queryapi/queryapi_v1_data_query.go`

```go
if err != nil {
    // 檢查是否為表不存在的錯誤
    if codeErr, ok := err.(gcode.Code); ok && codeErr.Code() == consts.ErrTableNotExists.Code() {
        res.Code = consts.ErrTableNotExists.Code()
        res.Message = consts.ErrTableNotExists.Message()
    } else {
        res.Code = consts.Failed.Code()
        res.Message = err.Error()
    }
}
```

### 4. 更新 API 規格文檔

**文件：** `ProductFile/api規格.md`

新增錯誤碼定義：
- `1001`: 表不存在
- `1002`: 數據庫錯誤  
- `1003`: 無效輸入
- `1004`: 未授權

### 5. 添加測試用例

**文件：** `internal/consts/errors_test.go`
- 驗證所有錯誤常量的正確定義

**文件：** `internal/logic/mariadb/error_handling_test.go`
- 測試表不存在錯誤處理邏輯

## 🎯 改進效果

### 1. 錯誤處理更精確
- 能夠準確識別表不存在的錯誤
- 提供具體的錯誤代碼和信息

### 2. 統一的錯誤響應
- 所有 API 返回一致的錯誤格式
- 便於前端和客戶端進行錯誤處理

### 3. 更好的日誌記錄
- 區分警告和錯誤級別的日誌
- 提供更詳細的錯誤上下文信息

### 4. 便於故障排除
- 明確的錯誤代碼便於快速定位問題
- 詳細的錯誤信息幫助開發者理解問題原因

## 🧪 測試驗證

### 編譯測試
```bash
go build -o /tmp/datasynchub .
# ✅ 編譯成功
```

### 單元測試
```bash
go test ./internal/consts/ -v
# ✅ 錯誤常量測試通過
```

## 📝 使用示例

### API 響應示例

**表不存在錯誤：**
```json
{
  "code": 1001,
  "message": "table does not exist",
  "cost": "5ms",
  "contents": []
}
```

**數據庫錯誤：**
```json
{
  "code": 1002,
  "message": "database error",
  "cost": "3ms",
  "contents": []
}
```

## 🔮 後續建議

1. **擴展錯誤處理**：可以考慮為其他常見數據庫錯誤添加專門的錯誤代碼
2. **監控告警**：基於錯誤代碼設置相應的監控和告警規則
3. **文檔完善**：持續更新 API 文檔中的錯誤處理說明
4. **測試覆蓋**：增加更多的錯誤場景測試用例

## ✅ 總結

通過這次改進，我們成功地：
- 解決了 GetAll 方法缺少特定錯誤代碼的問題
- 建立了統一的錯誤處理機制
- 提供了更好的用戶體驗和開發者體驗
- 為後續的錯誤處理擴展奠定了基礎

所有更改都已通過編譯測試，並且保持了向後兼容性。
