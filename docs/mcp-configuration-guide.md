# MCP (Model Context Protocol) 配置指南

## 概述

本文檔詳細說明 brainHub 項目中 MCP v2 模組的配置結構與使用方式。MCP 模組基於官方 [modelcontextprotocol/go-sdk](https://github.com/modelcontextprotocol/go-sdk) 實現，支援多種傳輸協議，提供靈活的配置管理和環境變數展開功能。

## 配置結構說明

### 1. 主配置結構 (MCPConfig)

```go
type MCPConfig struct {
    Enabled bool              `json:"enabled" yaml:"enabled"`
    Servers []MCPServerConfig `json:"servers" yaml:"servers"`
    Global  MCPGlobalConfig   `json:"global" yaml:"global"`
}
```

**欄位說明：**
- `enabled`: 是否啟用 MCP 功能
- `servers`: MCP 伺服器配置列表
- `global`: 全域配置，會自動套用到各個伺服器

### 2. 伺服器配置結構 (MCPServerConfig)

```go
type MCPServerConfig struct {
    Name        string            `json:"name" yaml:"name"`
    Type        string            `json:"type" yaml:"type"`
    Command     string            `json:"command,omitempty" yaml:"command,omitempty"`
    Args        []string          `json:"args,omitempty" yaml:"args,omitempty"`
    URL         string            `json:"url,omitempty" yaml:"url,omitempty"`
    Environment map[string]string `json:"environment,omitempty" yaml:"environment,omitempty"`
    Timeout     int               `json:"timeout,omitempty" yaml:"timeout,omitempty"`
}
```

**欄位說明：**
- `name`: 伺服器名稱（必填，唯一識別符）
- `type`: 傳輸協議類型（必填，支援：stdio、sse、streamable）
- `command`: 執行命令（stdio 類型必填）
- `args`: 命令參數（stdio 類型可選）
- `url`: 伺服器 URL（sse/streamable 類型必填）
- `environment`: 環境變數映射
- `timeout`: 連線超時時間（秒）

### 3. 全域配置結構 (MCPGlobalConfig)

```go
type MCPGlobalConfig struct {
    Timeout     int               `json:"timeout,omitempty" yaml:"timeout,omitempty"`
    Environment map[string]string `json:"environment,omitempty" yaml:"environment,omitempty"`
}
```

**欄位說明：**
- `timeout`: 全域超時設定（秒），會套用到未設定 timeout 的伺服器
- `environment`: 全域環境變數，會合併到各伺服器的環境變數中

## 支援的傳輸協議

### 1. STDIO 傳輸 (`stdio`)

透過標準輸入/輸出與本地程序通訊。

**必要參數：**
- `command`: 執行的命令
- `args`: 命令參數（可選）

**使用場景：**
- 本地 MCP 伺服器
- 命令行工具整合

### 2. SSE 傳輸 (`sse`)

使用 Server-Sent Events 進行 HTTP 通訊。

**必要參數：**
- `url`: SSE 端點 URL

**使用場景：**
- 遠端 MCP 伺服器
- 需要單向資料流的場景

### 3. Streamable 傳輸 (`streamable`)

使用雙向 HTTP 串流通訊，提供更好的可靠性。

**必要參數：**
- `url`: Streamable 端點 URL

**使用場景：**
- 遠端 MCP 伺服器
- 需要雙向通訊的場景
- 推薦用於生產環境

### 4. HTTP 傳輸 (`http`) - 已棄用

⚠️ **注意：HTTP 傳輸已棄用，請遷移至 `streamable` 傳輸。**

## 環境變數展開功能

MCP 配置支援 `${VAR}` 語法進行環境變數展開，適用於以下欄位：

- `command`: 執行命令
- `args`: 命令參數
- `url`: 伺服器 URL
- `environment`: 環境變數值

**展開規則：**
- 使用 Go 標準庫 `os.ExpandEnv` 函數
- 支援 `${VAR}` 和 `$VAR` 語法
- 未定義的變數會保持原樣

## Global 配置合併機制

Global 配置會自動套用到各個伺服器配置：

1. **超時設定合併：**
   - 如果伺服器未設定 `timeout`，會使用 `global.timeout`

2. **環境變數合併：**
   - Global 環境變數會合併到伺服器環境變數中
   - 伺服器層級的環境變數優先級更高（不會被覆蓋）

**合併示例：**
```yaml
global:
  timeout: 30
  environment:
    GLOBAL_VAR: "global_value"
    SHARED_VAR: "global_shared"

servers:
  - name: "example"
    type: "stdio"
    command: "mcp-server"
    environment:
      SHARED_VAR: "server_shared"  # 優先級更高
      LOCAL_VAR: "local_value"
```

**合併後的伺服器配置：**
- `timeout`: 30（來自 global）
- `environment`:
  - `GLOBAL_VAR`: "global_value"（來自 global）
  - `SHARED_VAR`: "server_shared"（伺服器優先）
  - `LOCAL_VAR`: "local_value"（伺服器專有）

## 配置驗證規則

### 基本驗證
- 啟用 MCP 時必須至少配置一個伺服器
- 每個伺服器必須有唯一的 `name`
- 必須指定支援的 `type`

### 傳輸類型驗證
- **stdio**: 必須提供 `command`
- **sse/streamable**: 必須提供 `url`
- **http**: 會顯示棄用警告並拒絕啟動

### 配置載入優先級
配置管理器會按以下順序搜尋配置：
1. `mcp_config`
2. `mcp_servers`
3. `mcp`

## 配置緩存機制

- 配置會緩存 5 分鐘以提升性能
- 可使用 `ConfigManager.ClearCache()` 清除緩存
- 支援配置熱重載（通過清除緩存實現）
