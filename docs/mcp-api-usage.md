# MCP API 使用指南與最佳實踐

## API 使用範例

### 1. 基本 MCP 整合

```go
package main

import (
    "context"
    "log"
    
    "brainHub/internal/llms/mcp"
)

func main() {
    ctx := context.Background()
    
    // 創建配置管理器
    configManager := mcp.NewConfigManager()
    config, err := configManager.LoadMCPConfig(ctx)
    if err != nil {
        log.Fatalf("Failed to load MCP config: %v", err)
    }
    
    if config == nil || !config.Enabled {
        log.Println("MCP is not enabled")
        return
    }
    
    // 創建 MCP 工具管理器
    logger := &customLogger{} // 實現 mcp.Logger 介面
    toolManager := mcp.NewMCPToolManager(config, logger)
    
    // 初始化連線
    if err := toolManager.Initialize(ctx); err != nil {
        log.Fatalf("Failed to initialize MCP: %v", err)
    }
    defer toolManager.Close(ctx)
    
    // 獲取可用工具
    tools, err := toolManager.GetToolDefinitions(ctx)
    if err != nil {
        log.Fatalf("Failed to get tools: %v", err)
    }
    
    log.Printf("Available tools: %d", len(tools))
    for _, tool := range tools {
        log.Printf("- %s: %s", tool.Name, tool.Description)
    }
}

// 實現 Logger 介面
type customLogger struct{}

func (l *customLogger) Warningf(ctx context.Context, format string, args ...interface{}) {
    log.Printf("[WARN] "+format, args...)
}

func (l *customLogger) Errorf(ctx context.Context, format string, args ...interface{}) {
    log.Printf("[ERROR] "+format, args...)
}

func (l *customLogger) Debugf(ctx context.Context, format string, args ...interface{}) {
    log.Printf("[DEBUG] "+format, args...)
}
```

### 2. 工具呼叫範例

```go
// 呼叫 MCP 工具
func callMCPTool(toolManager *mcp.MCPToolManager) {
    ctx := context.Background()
    
    // 準備工具參數
    args := map[string]interface{}{
        "query": "SELECT * FROM users WHERE active = true",
        "limit": 10,
    }
    
    // 呼叫工具
    result, err := toolManager.CallTool(ctx, "database.query", args)
    if err != nil {
        log.Printf("Tool call failed: %v", err)
        return
    }
    
    if result.Success {
        log.Printf("Tool result: %s", result.Content)
    } else {
        log.Printf("Tool execution failed: %s", result.Error)
    }
}
```

### 3. 連線狀態監控

```go
// 監控 MCP 連線狀態
func monitorMCPStatus(toolManager *mcp.MCPToolManager) {
    statuses := toolManager.GetConnectionStatus()
    
    for serverName, isConnected := range statuses {
        if isConnected {
            log.Printf("✅ Server '%s' is connected", serverName)
        } else {
            log.Printf("❌ Server '%s' is disconnected", serverName)
        }
    }
}
```

### 4. 配置驗證

```go
// 驗證 MCP 配置
func validateMCPConfig() {
    ctx := context.Background()
    
    configManager := mcp.NewConfigManager()
    config, err := configManager.LoadMCPConfig(ctx)
    if err != nil {
        log.Fatalf("Failed to load config: %v", err)
    }
    
    // 驗證配置
    if err := configManager.ValidateConfig(config); err != nil {
        log.Fatalf("Invalid config: %v", err)
    }
    
    log.Println("✅ MCP configuration is valid")
}
```

### 5. 遷移工具使用

```go
// 使用遷移工具
func runMigrationAnalysis() {
    ctx := context.Background()
    
    migrationTool := mcp.NewConfigMigrationTool()
    
    // 分析配置
    suggestions, err := migrationTool.AnalyzeConfiguration(ctx)
    if err != nil {
        log.Fatalf("Migration analysis failed: %v", err)
    }
    
    if len(suggestions) == 0 {
        log.Println("✅ No migration needed")
        return
    }
    
    // 生成遷移報告
    report, err := migrationTool.GenerateMigrationReport(ctx)
    if err != nil {
        log.Fatalf("Failed to generate report: %v", err)
    }
    
    fmt.Println(report)
    
    // 獲取棄用警告
    warnings := migrationTool.GetDeprecationWarnings(ctx)
    for _, warning := range warnings {
        log.Println(warning)
    }
}
```

## 最佳實踐

### 1. 配置管理

**✅ 推薦做法：**
```yaml
# 使用環境變數管理敏感資訊
mcp_config:
  global:
    environment:
      API_KEY: "${MCP_API_KEY}"
      DB_PASSWORD: "${DATABASE_PASSWORD}"
  
  servers:
    - name: "secure-service"
      type: "streamable"
      url: "${SERVICE_URL}"
      environment:
        AUTH_TOKEN: "${SERVICE_AUTH_TOKEN}"
```

**❌ 避免做法：**
```yaml
# 不要在配置檔案中硬編碼敏感資訊
servers:
  - name: "insecure-service"
    environment:
      API_KEY: "sk-1234567890abcdef"  # 不安全
```

### 2. 錯誤處理

**✅ 推薦做法：**
```go
// 完整的錯誤處理
toolManager := mcp.NewMCPToolManager(config, logger)

if err := toolManager.Initialize(ctx); err != nil {
    logger.Errorf(ctx, "MCP initialization failed: %v", err)
    // 實施降級策略
    return fallbackToLocalTools()
}

// 確保資源清理
defer func() {
    if err := toolManager.Close(ctx); err != nil {
        logger.Errorf(ctx, "Failed to close MCP connections: %v", err)
    }
}()
```

### 3. 超時設定

**✅ 推薦做法：**
```yaml
# 根據服務特性設定合理的超時
global:
  timeout: 30  # 預設超時

servers:
  - name: "fast-service"
    type: "streamable"
    url: "https://fast-api.example.com"
    timeout: 10  # 快速服務使用較短超時
    
  - name: "slow-service"
    type: "streamable"
    url: "https://slow-api.example.com"
    timeout: 120  # 慢速服務使用較長超時
```

### 4. 日誌記錄

**✅ 推薦做法：**
```go
// 實現結構化日誌
type structuredLogger struct {
    logger *slog.Logger
}

func (l *structuredLogger) Debugf(ctx context.Context, format string, args ...interface{}) {
    l.logger.DebugContext(ctx, fmt.Sprintf(format, args...))
}

func (l *structuredLogger) Warningf(ctx context.Context, format string, args ...interface{}) {
    l.logger.WarnContext(ctx, fmt.Sprintf(format, args...))
}

func (l *structuredLogger) Errorf(ctx context.Context, format string, args ...interface{}) {
    l.logger.ErrorContext(ctx, fmt.Sprintf(format, args...))
}
```

### 5. 連線管理

**✅ 推薦做法：**
```go
// 實施連線健康檢查
func healthCheck(toolManager *mcp.MCPToolManager) {
    statuses := toolManager.GetConnectionStatus()
    
    for serverName, isConnected := range statuses {
        if !isConnected {
            // 記錄連線問題
            log.Printf("Server '%s' is disconnected, attempting reconnection", serverName)
            
            // 可以實施重連邏輯或告警
            notifyDisconnection(serverName)
        }
    }
}
```

### 6. 效能優化

**✅ 推薦做法：**
```go
// 使用配置緩存
configManager := mcp.NewConfigManager()

// 配置會自動緩存 5 分鐘
config1, _ := configManager.LoadMCPConfig(ctx) // 從檔案載入
config2, _ := configManager.LoadMCPConfig(ctx) // 從緩存載入

// 需要時清除緩存
configManager.ClearCache()
```

### 7. 測試策略

**✅ 推薦做法：**
```go
// 單元測試範例
func TestMCPIntegration(t *testing.T) {
    // 使用測試配置
    config := &mcp.MCPConfig{
        Enabled: false, // 測試時禁用實際連線
    }
    
    logger := &testLogger{}
    toolManager := mcp.NewMCPToolManager(config, logger)
    
    err := toolManager.Initialize(context.Background())
    assert.NoError(t, err)
    
    // 測試基本功能
    tools, err := toolManager.GetToolDefinitions(context.Background())
    assert.NoError(t, err)
    assert.Empty(t, tools) // 禁用時應該沒有工具
}
```

## 常見問題與解決方案

### 1. 連線失敗

**問題：** `Failed to connect to MCP server`

**解決方案：**
1. 檢查網路連線
2. 驗證 URL 正確性
3. 確認伺服器支援指定的傳輸類型
4. 檢查防火牆設定

### 2. 認證失敗

**問題：** `Authentication failed`

**解決方案：**
1. 確認環境變數已正確設定
2. 檢查 API 金鑰有效性
3. 驗證認證方式是否正確

### 3. 工具呼叫失敗

**問題：** `Tool execution failed`

**解決方案：**
1. 檢查工具參數格式
2. 確認工具名稱正確
3. 查看詳細錯誤訊息
4. 檢查伺服器日誌

### 4. 配置載入失敗

**問題：** `Failed to load MCP config`

**解決方案：**
1. 檢查配置檔案路徑
2. 驗證 YAML 語法正確性
3. 確認配置檔案權限
4. 檢查配置結構是否符合規範
