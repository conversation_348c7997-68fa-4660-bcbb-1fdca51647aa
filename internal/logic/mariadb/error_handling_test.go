package mariadb

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"testing"

	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/test/gtest"
)

// TestGetContents_TableNotExists 測試表不存在的錯誤處理
func TestGetContents_TableNotExists(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		s := New()
		ctx := context.Background()

		// 測試查詢不存在的表
		req := model.GetContentsReq{
			Schema: "test_schema",
			Table:  "non_existent_table",
		}

		_, err := s.GetContents(ctx, req)

		// 驗證錯誤處理
		if err != nil {
			// 檢查錯誤代碼是否正確
			if codeErr, ok := err.(gcode.Code); ok {
				t.Logf("Error code: %d, Message: %s", codeErr.Code(), err.Error())
			} else {
				t.Logf("Error (no code): %s", err.Error())
			}

			// 注意：在實際測試環境中，可能需要模擬數據庫錯誤
			// 這裡主要是驗證錯誤處理邏輯的結構
		}
	})
}

// TestGetContents_RawSQL_TableNotExists 測試原始 SQL 查詢表不存在的錯誤處理
func TestGetContents_RawSQL_TableNotExists(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		s := New()
		ctx := context.Background()

		// 測試查詢不存在的表的原始 SQL
		req := model.GetContentsReq{
			Schema: "test_schema",
			RawSQL: "SELECT * FROM non_existent_table",
		}

		_, err := s.GetContents(ctx, req)

		// 驗證錯誤處理
		if err != nil {
			if codeErr, ok := err.(gcode.Code); ok {
				t.Logf("Raw SQL Error code: %d, Message: %s", codeErr.Code(), err.Error())
			} else {
				t.Logf("Raw SQL Error (no code): %s", err.Error())
			}
		}
	})
}

// TestErrorConstants 測試錯誤常量定義
func TestErrorConstants(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 驗證錯誤常量是否正確定義
		t.Assert(consts.ErrTableNotExists.Code(), 1001)
		t.Assert(consts.ErrTableNotExists.Message(), "table does not exist")
		
		t.Assert(consts.ErrDatabaseError.Code(), 1002)
		t.Assert(consts.ErrDatabaseError.Message(), "database error")
		
		t.Assert(consts.ErrInvalidInput.Code(), 1003)
		t.Assert(consts.ErrInvalidInput.Message(), "invalid input")
		
		t.Assert(consts.ErrUnauthorized.Code(), 1004)
		t.Assert(consts.ErrUnauthorized.Message(), "unauthorized")
	})
}
