package vector

import (
	"context"
	"dataSyncHub/boot"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/model/embedding"
	"dataSyncHub/internal/service"
	"dataSyncHub/utility"
	"fmt"
	"sync"

	"github.com/go-openapi/strfmt"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/google/uuid"
	amqp "github.com/rabbitmq/amqp091-go"
	weaClient "github.com/weaviate/weaviate-go-client/v5/weaviate"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/filters"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/graphql"
	"github.com/weaviate/weaviate/entities/models"
)

func init() {
	service.RegisterVecWeaviate(New())
}

type sVecWeaviate struct {
	sync.RWMutex
	client              *weaClient.Client                //weaviate.Client object
	VectorSettings      []*model.VectorCollectionSetting `json:"vector_settings"`
	TenantToCollections g.MapStrAny                      `json:"tenant_to_collections"`
}

func New() service.IVecWeaviate {
	var s = &sVecWeaviate{
		VectorSettings:      make([]*model.VectorCollectionSetting, 0),
		TenantToCollections: make(g.MapStrAny),
	}
	boot.WaitReady()

	ctx := gctx.GetInitCtx()

	s.initializeVectorServiceDelayed(ctx)

	return s
}

func (s *sVecWeaviate) OnMessage(ctx context.Context, message any) {
	var msg *amqp.Delivery
	_ = gconv.Struct(message, &msg)
	if msg == nil {
		s.logger().Error(ctx, "message is nil")
		return
	}
	bodyStr := gjson.New(msg.Body).MustToJsonIndentString()
	convertedJson, e := utility.ProcessVectorJSON(bodyStr)

	if e == nil {
		bodyStr = convertedJson
	}

	s.logger().Debugf(ctx, "OnMessage: type=%v , body= %v", msg.Type, bodyStr)
	switch msg.Type {
	default:
		s.logger().Error(ctx, "message type [%s] is not supported", msg.Type)
		return
	//case consts.ActionCreateCollection:
	//	var in *model.CreateCollectionInput
	//	_ = gconv.Struct(msg.Body, &in)
	//	if in == nil {
	//		s.logger().Error(ctx, "message body is nil")
	//		return
	//	}
	//	_ = s.CreateCollection(ctx, *in)
	//
	case consts.ActionAddNewProperties:
		var in *model.AddNewPropertiesInput
		_ = gconv.Struct(msg.Body, &in)
		if in == nil {
			s.logger().Error(ctx, "message body is nil")
		}
		_ = s.AddNewProperties(ctx, in)
	case consts.ActionCreateData:
		var in *model.CreateDataInput
		_ = gconv.Struct(msg.Body, &in)
		if in == nil {
			s.logger().Error(ctx, "message body is nil")
			return
		}
		_, _ = s.CreateData(ctx, in)

	case consts.ActionUpdateProperties:
		var in *model.UpdatePropertiesInput
		_ = gconv.Struct(msg.Body, &in)
		if in == nil {
			s.logger().Error(ctx, "message body is nil")
			return
		}
		_ = s.UpdateProperties(ctx, in)

	case consts.ActionCreateTenant:
		var in *model.CreateTenantIfNotExistInput
		_ = gconv.Struct(msg.Body, &in)
		if in == nil {
			s.logger().Error(ctx, "message body is nil")
			return
		}
		_ = s.CreateTenantIfNotExist(ctx, in)

	case consts.ActionClearDataByFilter:
		var in *model.ClearDataByFilterInput
		_ = gconv.Struct(msg.Body, &in)
		if in == nil {
			s.logger().Error(ctx, "message body is nil")
			return
		}
		_ = s.ClearDataByFilter(ctx, in)

	case consts.ActionEmptyCollection:
		var in *model.EmptyCollectionInput
		_ = gconv.Struct(msg.Body, &in)
		if in == nil {
			s.logger().Error(ctx, "message body is nil")
		}
		_ = s.EmptyCollection(ctx, in)

	case consts.ActionDeleteCollection:
		collections := gconv.Strings(msg.Body)
		_ = s.DeleteCollection(ctx, collections)
	case consts.ActionDeleteTenants:
		var in *model.DeleteTenantsInput
		_ = gconv.Struct(msg.Body, &in)
		if in == nil {
			s.logger().Error(ctx, "message body is nil")
			return
		}
		_ = s.DeleteTenants(ctx, in)
	case consts.ActionUpdateVector:
		var in *model.UpdateVectorInput
		_ = gconv.Struct(msg.Body, &in)
		if in == nil {
			s.logger().Error(ctx, "message body is nil")
			return
		}

		_ = s.UpdateVector(ctx, in)
	}

}
func (s *sVecWeaviate) UpdateVector(ctx context.Context, in *model.UpdateVectorInput) (err error) {
	s.logger().Infof(ctx, "UpdateVector : %v", in.String())
	updater := s.client.Data().Updater()
	if len(in.Vector) == 0 {
		err = gerror.New("vector is empty")
		s.logger().Error(ctx, err)
		return
	}

	if len(gstr.Trim(in.Tenant)) > 0 {
		updater.WithTenant(in.Tenant)
	}
	err = updater.WithClassName(in.Collection).
		WithID(in.ID).
		WithMerge().
		WithVector(in.Vector).
		Do(ctx)

	if err != nil {
		s.logger().Error(ctx, err)
	}

	return
}

func (s *sVecWeaviate) initializeVectorServiceDelayed(ctx context.Context) {

	if err := s.connect(ctx); err != nil {
		panic(err)
	}

	settings, err := service.Settings().Get(ctx, model.GetSettingsInput{
		SettingName: consts.SettingsVector,
	})
	if err != nil {
		s.logger().Debug(ctx, err)
		return
	}

	_ = gconv.Structs(gjson.New(settings).GetJsons("vector_settings"), &s.VectorSettings)
	settings, err = service.Settings().Get(ctx, model.GetSettingsInput{
		SettingName: consts.SettingsTenant,
	})
	if err != nil {
		s.logger().Debug(ctx, err)
		return
	}
	_ = gjson.New(settings).Scan(&s.TenantToCollections)

	s.createCollectionIfNotExist(ctx)

}
func (s *sVecWeaviate) createCollectionIfNotExist(ctx context.Context) {

	mapCollectionToVecSetting := gmap.NewStrAnyMap(true)

	for _, vecSetting := range s.VectorSettings {
		mapCollectionToVecSetting.Set(vecSetting.CollectionName, vecSetting)
	}
	collectionExistChecker := s.client.Schema().ClassExistenceChecker()

	fnGetTenants := func(colName string) []string {
		var tenants = make([]string, 0)
		for tenant, collections := range s.TenantToCollections {
			if garray.NewStrArrayFrom(gconv.Strings(collections)).Contains(colName) {
				tenants = append(tenants, tenant)
			}
		}

		return tenants
	}

	mapCollectionToVecSetting.Iterator(func(collection string, vecSetting interface{}) bool {
		var vSetting *model.VectorCollectionSetting
		_ = gconv.Struct(vecSetting, &vSetting)
		if exist, e := collectionExistChecker.WithClassName(collection).Do(ctx); e != nil {
			s.logger().Error(ctx, e)
		} else if !exist {
			//create a collection

			if e := s.CreateCollection(ctx, model.CreateCollectionInput{
				VectorCollectionSetting: *vSetting,
				Tenants:                 fnGetTenants(collection),
			}); e != nil {
				s.logger().Error(ctx, e)
			}

		}
		return true
	})
}
func (s *sVecWeaviate) logger() glog.ILogger {

	return g.Log().Cat(consts.CatWeaviate)
}

func (s *sVecWeaviate) connect(ctx context.Context) (err error) {
	s.logger().Info(ctx, "Connect to weaviate ...")
	vHost, err := g.Cfg().Get(ctx, "weaviate.host", "")
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	vScheme, err := g.Cfg().Get(ctx, "weaviate.scheme", "")
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	if vHost.IsEmpty() || vScheme.IsEmpty() {

		err = gerror.New("weaviate host or scheme is empty")
		s.logger().Error(ctx, err)
		return
	}
	vEmbeddings, err := g.Cfg().Get(ctx, "vectorEmbeddings")
	if err != nil {
		err = gerror.New("vectorEmbeddings is empty")
		s.logger().Error(ctx, err)
		return
	}
	var embCfg *embedding.VectorEmbeddings
	_ = vEmbeddings.Struct(&embCfg)
	if embCfg == nil {
		err = gerror.New("vectorEmbeddings is empty")
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Debugf(ctx, "Embeeding settings: %v ", gjson.New(embCfg).MustToJsonIndentString())
	cfg := weaClient.Config{
		Host:   vHost.String(),
		Scheme: vScheme.String(),
	}

	provider := embCfg.Embedding.Provider
	switch gstr.ToLower(provider) {
	default:
		err = gerror.Newf("weaviate provider  [%q]is not supported", provider)
		return
	case consts.ProviderAzure:
		if g.IsEmpty(embCfg.Embedding.Azure.ResourceName) ||
			g.IsEmpty(embCfg.Embedding.Azure.DeploymentId) ||
			g.IsEmpty(embCfg.Embedding.Azure.ApiKey) {
			err = gerror.New("azure embedding settings is empty")
			return
		}

		cfg.Headers = g.MapStrStr{consts.HeaderAzureAPIKey: embCfg.Embedding.Azure.ApiKey}

	case consts.ProviderGoogleVertex:
		if g.IsEmpty(embCfg.Embedding.Google.ProjectId) ||
			g.IsEmpty(embCfg.Embedding.Google.ModelId) {

			err = gerror.New("google embedding settings is empty ( project id or model id is empty)")
			return
		}

	case consts.ProviderGoogleStudio:
		if g.IsEmpty(embCfg.Embedding.Google.StudioAPIKey) {
			err = gerror.New("google embedding studio api key  is empty")
			return
		}
		cfg.Headers = g.MapStrStr{consts.HeaderGoogleStudioAPIKey: embCfg.Embedding.Google.StudioAPIKey}

	}

	s.client, err = weaClient.NewClient(cfg)

	return

}

func (s *sVecWeaviate) updateCollectionProperties(ctx context.Context, collection string, properties []*models.Property) {
	s.Lock()
	defer s.Unlock()

	mapCollectionToSetting := gmap.NewStrAnyMap()
	for _, setting := range s.VectorSettings {
		mapCollectionToSetting.Set(setting.CollectionName, setting)
	}

	if mapCollectionToSetting.Contains(collection) {
		vecColSetting := mapCollectionToSetting.GetVar(collection)
		var setting *model.VectorCollectionSetting
		_ = vecColSetting.Struct(&setting)
		setting.Properties = append(setting.Properties, properties...)
		mapCollectionToSetting.Set(collection, setting)
		_ = gconv.Structs(mapCollectionToSetting.Values(), &s.VectorSettings)

		if e := service.Settings().Set(ctx, model.SetSettingsInput{
			SettingName: consts.SettingsVector,
			Settings:    gjson.New(s.VectorSettings).MustToJsonIndentString(),
		}); e != nil {
			s.logger().Error(ctx, e)
		}

	}

}
func (s *sVecWeaviate) updateVectorCollectionSetting(ctx context.Context, in *model.VectorCollectionSetting) {
	s.Lock()
	defer s.Unlock()

	mapCollectionToSetting := gmap.NewStrAnyMap()
	for _, setting := range s.VectorSettings {
		mapCollectionToSetting.Set(setting.CollectionName, setting)
	}

	if !mapCollectionToSetting.Contains(in.CollectionName) {
		s.VectorSettings = append(s.VectorSettings, in)
		if e := service.Settings().Set(ctx, model.SetSettingsInput{
			SettingName: consts.SettingsVector,
			Settings:    gjson.New(s.VectorSettings).MustToJsonIndentString(),
		}); e != nil {
			s.logger().Error(ctx, e)
		}

	}

}

func (s *sVecWeaviate) updateTenantCollection(ctx context.Context, collection string, tenants []string) {
	s.Lock()
	defer s.Unlock()

	mapTenantToCollections := gmap.NewStrAnyMapFrom(s.TenantToCollections)

	var changed = false
	for _, tenant := range tenants {
		if mapTenantToCollections.Contains(tenant) {
			collections := mapTenantToCollections.GetVar(tenant)
			setCollections := gset.NewStrSetFrom(collections.Strings())
			if !setCollections.Contains(collection) {
				setCollections.Add(collection)
				changed = true
			}
			mapTenantToCollections.Set(tenant, setCollections.Slice())

		} else {
			changed = true
			mapTenantToCollections.Set(tenant, []string{collection})
		}
	}

	s.TenantToCollections = mapTenantToCollections.MapStrAny()
	if changed {
		if e := service.Settings().Set(ctx, model.SetSettingsInput{
			SettingName: consts.SettingsTenant,
			Settings:    gjson.New(s.TenantToCollections).MustToJsonIndentString(),
		}); e != nil {
			s.logger().Error(ctx, e)
		}

	}
}

func (s *sVecWeaviate) vectorConfig(ctx context.Context, vectorProperties []string) map[string]models.VectorConfig {
	var retVecConfig = make(map[string]models.VectorConfig)
	var (
		resourceName, deploymentId = "", ""
		projectId, modelId         = "", ""
	)

	vEmbeddings, err := g.Cfg().Get(ctx, "vectorEmbeddings")
	if err != nil {
		err = gerror.New("vectorEmbeddings is empty")
		s.logger().Error(ctx, err)
		return retVecConfig
	}
	var embCfg *embedding.VectorEmbeddings
	_ = vEmbeddings.Struct(&embCfg)
	switch gstr.ToLower(embCfg.Embedding.Provider) {
	default:
		s.logger().Error(ctx, "weaviate provider  [%q]is not supported", embCfg.Embedding.Provider)
		return retVecConfig
	case consts.ProviderAzure:
		resourceName = embCfg.Embedding.Azure.ResourceName
		deploymentId = embCfg.Embedding.Azure.DeploymentId
		for _, property := range vectorProperties {
			retVecConfig[fmt.Sprintf("%s_vector", property)] = models.VectorConfig{
				VectorIndexType: "hnsw",
				Vectorizer: g.Map{
					"text2vec-openai": g.Map{
						"properties":   []string{property},
						"resourceName": resourceName,
						"deploymentId": deploymentId,
					},
				},
			}
		}
		return retVecConfig
	case consts.ProviderGoogleVertex:
		projectId = embCfg.Embedding.Google.ProjectId
		modelId = embCfg.Embedding.Google.ModelId
		for _, property := range vectorProperties {
			retVecConfig[fmt.Sprintf("%s_vector", property)] = models.VectorConfig{
				VectorIndexType: "hnsw",
				Vectorizer: g.Map{
					"text2vec-google": g.Map{
						"properties": []string{property},
						"projectId":  projectId,
						"modelId":    modelId,
					},
				},
			}
		}
		return retVecConfig

	}

}
func (s *sVecWeaviate) existCollection(ctx context.Context, collection string) (exist bool, err error) {

	s.logger().Infof(ctx, "Check if collection %s exists", collection)
	exist, err = s.client.Schema().ClassExistenceChecker().WithClassName(collection).Do(ctx)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	return
}
func (s *sVecWeaviate) CreateCollection(ctx context.Context, in model.CreateCollectionInput) (err error) {
	s.logger().Infof(ctx, "Create collection : %v", gjson.New(in).MustToJsonString())
	exist, err := s.existCollection(ctx, in.CollectionName)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	if exist {
		s.logger().Debugf(ctx, "collection %s already exists", in.CollectionName)
		return
	}

	vectorConfig := s.vectorConfig(ctx, in.VectorProperties)
	if len(vectorConfig) == 0 {
		err = gerror.New("vectorConfig is empty")
		s.logger().Error(ctx, err)
		return
	}
	collectionDef := &models.Class{
		Class:               in.CollectionName,
		Properties:          in.Properties,
		InvertedIndexConfig: &models.InvertedIndexConfig{IndexNullState: true},
		VectorConfig:        vectorConfig,
	}
	if in.MultiTenancy {
		collectionDef.MultiTenancyConfig = &models.MultiTenancyConfig{Enabled: true}
	}

	err = s.client.Schema().
		ClassCreator().
		WithClass(collectionDef).
		Do(ctx)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	if in.RenewSettings {
		//asynchronous update
		g.Go(gctx.NeverDone(ctx), func(ctx context.Context) {
			s.updateVectorCollectionSetting(ctx, &in.VectorCollectionSetting)
		}, func(ctx context.Context, exception error) {
			s.logger().Error(ctx, exception)
		})
	}

	if len(in.Tenants) > 0 {
		var tenants = make([]models.Tenant, 0)
		for _, tenant := range in.Tenants {
			tenants = append(tenants, models.Tenant{Name: tenant})
		}
		if err = s.client.Schema().TenantsCreator().WithClassName(in.CollectionName).WithTenants(tenants...).Do(ctx); err != nil {
			s.logger().Error(ctx, err)
			return
		}
		if in.RenewSettings {
			// asynchronous update
			g.Go(gctx.NeverDone(ctx), func(ctx context.Context) {
				s.updateTenantCollection(ctx, in.CollectionName, in.Tenants)
			}, func(ctx context.Context, exception error) {
				s.logger().Error(ctx, exception)
			})

		}

	}

	return
}

func (s *sVecWeaviate) AddNewProperties(ctx context.Context, in *model.AddNewPropertiesInput) (err error) {

	s.logger().Infof(ctx, "AddNewProperties : %v", gjson.New(in).MustToJsonIndentString())
	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}
	for _, property := range in.Properties {
		if err = s.client.Schema().
			PropertyCreator().
			WithClassName(in.CollectionName).
			WithProperty(property).
			Do(ctx); err != nil {
			s.logger().Error(ctx, err)
			return
		}
	}
	g.Go(gctx.NeverDone(ctx), func(ctx context.Context) {
		s.updateCollectionProperties(ctx, in.CollectionName, in.Properties)
	}, func(ctx context.Context, exception error) {
		s.logger().Error(ctx, exception)
	})

	return
}
func (s *sVecWeaviate) clearDataByFilter(ctx context.Context, in *model.ClearDataByFilterInput) (resp *models.BatchDeleteResponse, err error) {

	batcher := s.client.Batch().ObjectsBatchDeleter().
		WithClassName(in.CollectionName).
		WithDryRun(false).
		WithOutput("minimal")
	s.logger().Noticef(ctx, "Filter: %s", in.Filter)
	filter, err := in.GetWhereBuilder()
	if err != nil {
		s.logger().Error(ctx, err)
		return nil, err
	}

	if filter != nil {
		batcher.WithWhere(filter)
	}
	if !g.IsEmpty(in.Tenant) {
		batcher.WithTenant(in.Tenant)
	}
	resp, err = batcher.Do(ctx)

	return

}
func (s *sVecWeaviate) ClearDataByFilter(ctx context.Context, in *model.ClearDataByFilterInput) (err error) {
	resp, err := s.clearDataByFilter(ctx, in)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Debugf(ctx, "ClearDataByFilter resp: %v", gjson.New(resp).MustToJsonIndentString())

	return
}
func (s *sVecWeaviate) EmptyCollection(ctx context.Context, in *model.EmptyCollectionInput) (err error) {
	s.logger().Infof(ctx, "EmptyCollection : %v", gjson.New(in).MustToJsonIndentString())
	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}

	var filter = filters.Where().
		WithOperator(filters.NotEqual).
		WithPath([]string{"id"}).
		WithValueText(uuid.NewString())

	resp, err := s.clearDataByFilter(ctx, &model.ClearDataByFilterInput{
		CollectionName: in.CollectionName,
		Tenant:         in.Tenant,
		Filter:         filter.String(),
	})

	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Debugf(ctx, "EmptyCollection resp: %v", gjson.New(resp).MustToJsonIndentString())
	return
}

func (s *sVecWeaviate) DeleteCollection(ctx context.Context, collections []string) (err error) {
	s.logger().Infof(ctx, "DeleteCollection : %v", gstr.Join(collections, ","))
	if g.IsEmpty(collections) {
		err = gerror.New("collections is empty")
		s.logger().Error(ctx, err)
		return
	}
	collectionChecker := s.client.Schema().ClassExistenceChecker()
	collectionDeleter := s.client.Schema().ClassDeleter()
	for _, collection := range collections {
		if exist, e := collectionChecker.WithClassName(collection).Do(ctx); e != nil {
			s.logger().Error(ctx, e)
		} else if exist {
			if e := collectionDeleter.WithClassName(collection).Do(ctx); e != nil {
				s.logger().Error(ctx, e)
			}
		}
	}

	return
}
func (s *sVecWeaviate) DeleteTenants(ctx context.Context, in *model.DeleteTenantsInput) (err error) {
	s.logger().Infof(ctx, "DeleteTenants : %v", gjson.New(in).MustToJsonIndentString())
	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}
	tenantDeleter := s.client.Schema().TenantsDeleter()
	tenantExistenceChecker := s.client.Schema().TenantsExists()
	for _, collection := range in.Collections {
		for _, tenant := range in.Tenants {
			if exist, e := tenantExistenceChecker.WithClassName(collection).WithTenant(tenant).Do(ctx); e != nil {
				s.logger().Error(ctx, e)
			} else if exist {
				if e := tenantDeleter.WithClassName(collection).WithTenants(tenant).Do(ctx); e != nil {
					s.logger().Error(ctx, e)
				}
			}
		}

	}

	return
}
func (s *sVecWeaviate) CreateSingleData(ctx context.Context, in model.CollectionData) (id string, err error) {
	s.logger().Infof(ctx, "CreateSingleData : %v", in.String())
	creator := s.client.Data().Creator()
	if len(in.Tenant) > 0 {
		_ = s.CreateTenantIfNotExist(ctx, &model.CreateTenantIfNotExistInput{
			Tenant:      in.Tenant,
			Collections: []string{in.Collection},
		})

		creator.WithTenant(in.Tenant)
	}
	if len(in.ID) > 0 {
		creator.WithID(in.ID)
	}
	if len(in.Vector) > 0 {
		creator.WithVector(in.Vector)
	}

	if len(in.Properties) > 0 {
		creator.WithProperties(in.Properties)
	}
	if len(in.Collection) > 0 {
		creator.WithClassName(in.Collection)
	}

	resp, err := creator.Do(ctx)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	id = resp.Object.ID.String()
	return
}
func (s *sVecWeaviate) CreateData(ctx context.Context, in *model.CreateDataInput) (out *model.CreateDataOutput, err error) {
	s.logger().Infof(ctx, "CreateData : %v", in.String())
	if len(in.Data) == 0 {
		s.logger().Debug(ctx, "CreateData : data is empty")
		return
	}
	out = &model.CreateDataOutput{}
	batcher := s.client.Batch().ObjectsBatcher()
	for _, data := range in.Data {

		dataObj := &models.Object{
			Class:      data.Collection,
			Properties: data.Properties,
		}
		if !g.IsEmpty(data.Tenant) {
			_ = s.CreateTenantIfNotExist(ctx, &model.CreateTenantIfNotExistInput{
				Tenant:      data.Tenant,
				Collections: []string{data.Collection},
			})
			dataObj.Tenant = data.Tenant
		}
		if !g.IsEmpty(data.ID) {
			dataObj.ID = strfmt.UUID(data.ID)
		}
		if len(data.Vector) > 0 {
			dataObj.Vector = data.Vector
		}
		batcher.WithObjects(dataObj)

	}
	resp, err := batcher.Do(ctx)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	out.Total = len(resp)

	for _, response := range resp {

		if response.Result != nil && response.Result.Errors != nil && len(response.Result.Errors.Error) > 0 {
			s.logger().Error(ctx, response.Result.Errors.Error[0].Message)
			out.Fail++
		} else {
			out.Success++
			out.IDs = append(out.IDs, response.ID.String())
		}

	}

	return
}
func (s *sVecWeaviate) FetchRecords(ctx context.Context, in model.FetchRecordsInput) (out *model.FetchRecordsOutput, err error) {
	out = &model.FetchRecordsOutput{Records: make([]map[string]any, 0)}
	s.logger().Infof(ctx, "FetchRecords: %v  ", in.String())

	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}
	fnReCreateMap := func(m map[string]any) {
		for k, v := range m {
			if k == "_additional" && in.OriginalAdditional == false {
				if additionalMap, ok := v.(map[string]any); ok {
					for ak, av := range additionalMap {
						m[ak] = av
					}
					delete(m, "_additional")
				}
			}
		}
	}

	fields := make([]graphql.Field, 0)

	for _, property := range in.Properties {
		switch property {
		default:
			fields = append(fields, graphql.Field{Name: property})
		case "id", "vector", "score", "explainScore", "distance":
			fields = append(fields, graphql.Field{Name: "_additional", Fields: []graphql.Field{{Name: property}}})
		}
	}

	searcher := s.client.GraphQL().Get().
		WithClassName(in.Collection).
		WithFields(fields...).
		WithLimit(in.PageSize)
	if !g.IsEmpty(in.Tenant) {
		searcher.WithTenant(in.Tenant)
	}

	filter, e := in.GetWhereBuilder()
	if e != nil {
		s.logger().Error(ctx, e)
	}

	if filter != nil {
		searcher.WithWhere(filter)
	}
	if in.Offset > 0 {
		searcher.WithOffset(in.Offset)
	}
	resp, err := searcher.Do(ctx)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	if len(resp.Errors) > 0 {
		err = gerror.New(resp.Errors[0].Message)
		s.logger().Error(ctx, err)
		return
	}
	dataSet := gjson.New(resp).GetJsons(fmt.Sprintf("data.Get.%s", in.Collection))
	for _, data := range dataSet {
		mProperty := gconv.Map(data)
		fnReCreateMap(mProperty)
		out.Records = append(out.Records, mProperty)
	}

	return
}
func (s *sVecWeaviate) GetAllRecords(ctx context.Context, in model.GetAllRecordsInput) (out *model.GetAllRecordsOutput, err error) {
	s.logger().Infof(ctx, "GetAllRecords: %v", in.String())
	fnReCreateMap := func(m map[string]any) {
		for k, v := range m {
			if k == "_additional" {
				if additionalMap, ok := v.(map[string]any); ok {
					for ak, av := range additionalMap {
						m[ak] = av
					}
					delete(m, "_additional")
				}
			}
		}
	}

	out = &model.GetAllRecordsOutput{Records: make([]map[string]any, 0)}
	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}

	fields := make([]graphql.Field, 0)

	for _, property := range in.Properties {
		switch property {
		default:
			fields = append(fields, graphql.Field{Name: property})
		case "id", "vector", "score", "explainScore", "distance":
			fields = append(fields, graphql.Field{Name: "_additional", Fields: []graphql.Field{{Name: property}}})
		}
	}

	searcher := s.client.GraphQL().Get().
		WithClassName(in.Collection).
		WithFields(fields...).
		WithLimit(in.PageSize)

	if !g.IsEmpty(in.Tenant) {
		searcher.WithTenant(in.Tenant)
	}
	filter, e := in.GetWhereBuilder()
	if e != nil {
		s.logger().Debug(ctx, e)
	}
	if filter != nil {

		searcher.WithWhere(filter)
	}
	offset := 0

	for {
		if offset > 0 {
			searcher.WithOffset(offset)
		}

		searchResp, err := searcher.Do(ctx)
		if err != nil {
			s.logger().Error(ctx, err)
			return nil, err
		}

		if len(searchResp.Errors) > 0 {

			return nil, gerror.New(searchResp.Errors[0].Message)
		}
		dataSet := gjson.New(searchResp).GetJsons("data.Get." + in.Collection)

		if len(dataSet) == 0 {
			break
		}
		offset += len(dataSet)

		for _, data := range dataSet {
			mProperty := gconv.Map(data)
			fnReCreateMap(mProperty)
			out.Records = append(out.Records, mProperty)
		}
	}

	return
}
func (s *sVecWeaviate) GetPropertyNames(ctx context.Context, collection string) []string {

	var retPropertyNames = make([]string, 0)
	s.logger().Infof(ctx, "GetPropertyNames: %v", collection)
	vCollection, err := s.client.Schema().ClassGetter().WithClassName(collection).Do(ctx)
	if err != nil {
		s.logger().Error(ctx, err)
	} else {
		for _, property := range vCollection.Properties {
			retPropertyNames = append(retPropertyNames, property.Name)
		}
	}
	if err != nil {
		return nil
	}
	return retPropertyNames
}
func (s *sVecWeaviate) GetProperties(ctx context.Context, in model.GetPropertiesInput) (out *model.GetPropertiesOutput, err error) {
	s.logger().Infof(ctx, "GetProperties: %v  ", gjson.New(in).MustToJsonIndentString())
	out = &model.GetPropertiesOutput{
		IDToProperties: make(map[string]map[string]any),
	}

	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}

	objectGetter := s.client.Data().ObjectsGetter().WithClassName(in.Collection)
	if !g.IsEmpty(gstr.Trim(in.Tenant)) {
		objectGetter.WithTenant(in.Tenant)
	}

	if in.WithVector {
		objectGetter.WithVector()
	}
	fnReCreateMap := func(m map[string]any) {
		for k, v := range m {
			if k == "_additional" {
				if additionalMap, ok := v.(map[string]any); ok {
					for ak, av := range additionalMap {
						m[ak] = av
					}
					delete(m, "_additional")
				}
			}
		}
	}
	for _, id := range in.IDs {
		obj, e := objectGetter.WithID(id).Do(ctx)
		if e != nil {
			s.logger().Error(ctx, e)
			continue
		} else {
			if len(obj) > 0 {
				mProperty := gconv.Map(obj[0].Properties)
				fnReCreateMap(mProperty)
				if in.WithVector {
					mProperty["vector"] = obj[0].Vector
				}
				out.IDToProperties[id] = mProperty
			}
		}
	}

	return
}
func (s *sVecWeaviate) GetPropertiesByGroup(ctx context.Context, in model.GetPropertiesByGroupInput) (out *model.GetPropertiesByGroupOutput, err error) {
	s.logger().Infof(ctx, "GetPropertiesByGroup: %v  ", gjson.New(in).MustToJsonIndentString())
	out = &model.GetPropertiesByGroupOutput{
		OutputMap: make(map[string]string),
	}
	fnReCreateMap := func(m map[string]any) {
		for k, v := range m {
			if k == "_additional" {
				if additionalMap, ok := v.(map[string]any); ok {
					for ak, av := range additionalMap {
						m[ak] = av
					}
					delete(m, "_additional")
				}
			}
		}
	}

	groupBuilder := &graphql.GroupByArgumentBuilder{}
	groupBuilder.WithPath(in.GroupedFields).WithGroups(in.MaxGroups).WithObjectsPerGroup(in.MaxObjectPerGroup)

	fields := make([]graphql.Field, 0)
	for _, property := range in.Properties {
		switch property {
		default:
			fields = append(fields, graphql.Field{
				Name: property,
			})
		case "id", "vector", "score", "explainScore", "distance":
			fields = append(fields, graphql.Field{
				Name: "_additional",
				Fields: []graphql.Field{
					{
						Name: property,
					},
				},
			})
		}

	}
	var resp *models.GraphQLResponse

	searcher := s.client.GraphQL().Get().
		WithClassName(in.Collection).
		WithGroupBy(groupBuilder).
		WithFields(fields...)
	filter, err := in.GetWhereBuilder()
	if err != nil {
		s.logger().Error(ctx, err)
		return nil, err
	}
	if filter != nil {
		searcher.WithWhere(filter)
	}
	if !g.IsEmpty(in.Tenant) {
		searcher.WithTenant(in.Tenant)
	}

	resp, err = searcher.
		Do(ctx)

	if err != nil {
		s.logger().Error(ctx, err)
		return
	} else {
		if len(resp.Errors) > 0 {
			err = gerror.New(resp.Errors[0].Message)
			s.logger().Error(ctx, err)
			return

		}
		data := gjson.New(resp).GetJsons(fmt.Sprintf("data.Get.%s", in.Collection))
		for _, jsData := range data {

			mProperty := gconv.Map(jsData)
			fnReCreateMap(mProperty)

			out.OutputData = append(out.OutputData, mProperty)

			if !g.IsEmpty(in.KeyProp) || !g.IsEmpty(in.ValueProp) {
				key := ""
				if in.KeyProp == "id" {
					key = jsData.Get("_additional.id").String()
				} else {
					key = jsData.Get(in.KeyProp).String()
				}

				value := jsData.Get(in.ValueProp).String()
				out.OutputMap[key] = value
			}

		}
	}

	return

}

func (s *sVecWeaviate) UpdateProperties(ctx context.Context, in *model.UpdatePropertiesInput) (err error) {
	s.logger().Infof(ctx, "UpdateProperties: %v  ", in.String())
	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}
	propertyUpdater := s.client.Data().Updater().WithClassName(in.Collection).WithID(in.ID)
	if !g.IsEmpty(in.Tenant) {
		propertyUpdater.WithTenant(in.Tenant)
	}

	if len(in.Vector) > 0 {
		propertyUpdater.WithVector(in.Vector)
	}

	if len(in.Properties) > 0 {
		propertyUpdater.WithProperties(in.Properties)
	}
	err = propertyUpdater.WithMerge().Do(ctx)

	if err != nil {
		s.logger().Error(ctx, err)
	}

	return
}

// CreateTenantIfNotExist ensures that a tenant exists for specified collections. Creates tenant if it does not exist.
// It validates the input, checks for tenant existence, and conditionally creates the tenant using the provided client.
// Returns an error if validation, existence check, or tenant creation fails.
func (s *sVecWeaviate) CreateTenantIfNotExist(ctx context.Context, in *model.CreateTenantIfNotExistInput) (err error) {
	s.logger().Infof(ctx, "CreateTenantIfNotExist: %v  ", gjson.New(in).MustToJsonIndentString())

	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}
	tenantExistenceChecker := s.client.Schema().TenantsExists()
	tenantCreator := s.client.Schema().TenantsCreator()

	for _, collection := range in.Collections {
		exists, err := tenantExistenceChecker.WithClassName(collection).WithTenant(in.Tenant).Do(ctx)
		if err != nil {
			s.logger().Error(ctx, err)
			return gerror.Wrapf(err, "Failed to check tenant existence. Tenant:%v, Collection: %v", in.Tenant, collection)
		}

		if !exists {
			err = tenantCreator.WithTenants(models.Tenant{Name: in.Tenant}).
				WithClassName(collection).
				Do(ctx)
			if err != nil {
				s.logger().Error(ctx, err)
				return gerror.Wrapf(err, "Failed to create tenant. Tenant:%v, Collection: %v", in.Tenant, collection)
			}
			s.logger().Debugf(ctx, "Created tenant %v for collection %v", in.Tenant, collection)
		}
	}

	return
}

func (s *sVecWeaviate) SimilaritySearch(ctx context.Context, in model.SimilaritySearchInput) (out *model.SimilaritySearchOutput, err error) {
	out = &model.SimilaritySearchOutput{Properties: make([]map[string]any, 0)}
	s.logger().Infof(ctx, "SimilaritySearch: %v  ", in.String())

	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}

	fields := make([]graphql.Field, 0)
	for _, property := range in.Properties {
		switch property {
		default:
			fields = append(fields, graphql.Field{Name: property})
		case "id", "vector", "score", "explainScore", "distance":
			fields = append(fields, graphql.Field{Name: "_additional", Fields: []graphql.Field{
				{Name: property},
			}})

		}
	}
	fnReCreateMap := func(m map[string]any) {
		for k, v := range m {
			if k == "_additional" && in.OriginalAdditional == false {
				if additionalMap, ok := v.(map[string]any); ok {
					for ak, av := range additionalMap {
						m[ak] = av
					}
					delete(m, "_additional")
				}
			}
		}
	}
	nearVector := &graphql.NearVectorArgumentBuilder{}
	nearVector.WithVector(in.Vector).WithDistance(in.Distance)
	var resp *models.GraphQLResponse
	simSearcher := s.client.GraphQL().Get().
		WithClassName(in.Collection).
		WithLimit(in.Limit).
		WithFields(fields...).
		WithNearVector(nearVector)
	if !g.IsEmpty(in.Tenant) {
		simSearcher.WithTenant(in.Tenant)
	}
	filter, e := in.GetWhereBuilder()
	if e != nil {
		s.logger().Debug(ctx, e)
	}
	if filter != nil {
		simSearcher.WithWhere(filter)
	}

	resp, err = simSearcher.
		Do(ctx)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	if resp == nil {
		err = gerror.New("response is nil")
		s.logger().Error(ctx, err)
		return
	}
	if len(resp.Errors) > 0 {
		err = gerror.New(resp.Errors[0].Message)
		s.logger().Error(ctx, err)
		return
	}
	jsData := gjson.New(resp).GetJsons(fmt.Sprintf("data.Get.%s", in.Collection))
	for _, data := range jsData {
		mProperty := gconv.Map(data)
		fnReCreateMap(mProperty)
		out.Properties = append(out.Properties, mProperty)
	}

	return
}

func (s *sVecWeaviate) HybridSearch(ctx context.Context, in model.HybridSearchInput) (out *model.HybridSearchOutput, err error) {
	out = &model.HybridSearchOutput{Properties: make([]map[string]any, 0)}
	s.logger().Infof(ctx, "HybridSearch: %v ", in.String())
	if err = g.Validator().Data(in).Run(ctx); err != nil {
		s.logger().Error(ctx, err)
		return

	}

	fields := make([]graphql.Field, 0)
	for _, property := range in.Properties {
		switch property {
		default:
			fields = append(fields, graphql.Field{Name: property})
		case "id", "vector", "score", "explainScore", "distance":
			fields = append(fields, graphql.Field{Name: "_additional", Fields: []graphql.Field{
				{Name: property},
			}})

		}
	}
	fnReCreateMap := func(m map[string]any) {
		for k, v := range m {
			if k == "_additional" && in.OriginalAdditional == false {
				if additionalMap, ok := v.(map[string]any); ok {
					for ak, av := range additionalMap {
						m[ak] = av
					}
					delete(m, "_additional")
				}
			}
		}
	}
	var resp *models.GraphQLResponse
	hybridBuilder := &graphql.HybridArgumentBuilder{}
	hybridBuilder.
		WithAlpha(in.Alpha).
		WithQuery(in.Query).
		WithFusionType(graphql.FusionType(in.FusionType)).
		WithVector(in.Vector)

	if len(in.PropertyWeight) > 0 {
		hybridBuilder.WithProperties(in.PropertyWeight)
	}

	hybrid := s.client.GraphQL().Get().
		WithClassName(in.Collection).
		WithFields(fields...).
		WithLimit(in.Limit).
		WithHybrid(hybridBuilder)
	filter, e := in.GetWhereBuilder()
	if e != nil {
		s.logger().Debug(ctx, e)
	}
	if filter != nil {
		hybrid.WithWhere(filter)
	}
	if !g.IsEmpty(in.Tenant) {
		hybrid.WithTenant(in.Tenant)
	}

	resp, err = hybrid.
		Do(ctx)

	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	if resp == nil {
		err = gerror.New("response is nil")
		s.logger().Error(ctx, err)
		return
	}
	if len(resp.Errors) > 0 {
		err = gerror.New(resp.Errors[0].Message)
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Debugf(ctx, "HybridSearch resp: %v", gjson.New(resp).MustToJsonIndentString())

	jsData := gjson.New(resp).GetJsons(fmt.Sprintf("data.Get.%s", in.Collection))
	for _, data := range jsData {
		mProperty := gconv.Map(data)
		fnReCreateMap(mProperty)
		out.Properties = append(out.Properties, mProperty)
	}

	return
}

func (s *sVecWeaviate) GetTenantAndCollections(ctx context.Context, collections []string) (out *gmap.StrAnyMap, err error) {
	s.logger().Infof(ctx, "GetTenantAndCollections : %v ", collections)
	out = gmap.NewStrAnyMap()
	tenantGetter := s.client.Schema().TenantsGetter()

	for _, collection := range collections {
		tenants, err := tenantGetter.WithClassName(collection).Do(ctx)
		if err != nil {
			s.logger().Errorf(ctx, "get tenants from  %s  : %v", collection, err)
			return nil, err
		}

		for _, tenant := range tenants {
			// 获取当前租户的集合列表，如果不存在则创建新的集合集
			var collectionsSet *gset.StrSet
			if !out.Contains(tenant.Name) {
				collectionsSet = gset.NewStrSet()
			} else {
				collectionsSet = gset.NewStrSetFrom(gconv.Strings(out.Get(tenant.Name)))
			}

			// 添加新的集合并更新映射
			collectionsSet.Add(collection)
			out.Set(tenant.Name, collectionsSet.Slice())

		}
	}

	return
}
