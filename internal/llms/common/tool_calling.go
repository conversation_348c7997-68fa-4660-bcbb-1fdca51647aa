package common

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/util/gconv"
)

// IToolCalling 工具調用接口 - 所有 LLM 都實現此接口
type IToolCalling interface {
	// GetToolDefinitions 獲取可用工具定義
	GetToolDefinitions(ctx context.Context) ([]ToolDefinition, error)

	// CallToolsIfNeeded 根據 LLM 判斷調用工具
	CallToolsIfNeeded(ctx context.Context, userMessage string, conversationHistory []Message) (*ToolCallResult, error)

	// ExecuteToolCall 執行具體的工具調用
	ExecuteToolCall(ctx context.Context, toolName string, args map[string]interface{}) (*ToolResult, error)
}

// ToolDefinition 工具定義結構
type ToolDefinition struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`  // JSON Schema 格式
	ClientName  string                 `json:"client_name"` // 所屬的工具客戶端名稱
}

// ToolCallResult 工具調用結果
type ToolCallResult struct {
	HasToolCalls bool                   `json:"has_tool_calls"`
	ToolCalls    []RequestedToolCall    `json:"tool_calls"`
	Response     string                 `json:"response"`
	ToolResults  map[string]*ToolResult `json:"tool_results"`
}

// RequestedToolCall LLM 請求的工具調用
type RequestedToolCall struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}

// ToolResult 工具執行結果
type ToolResult struct {
	Success bool   `json:"success"`
	Content string `json:"content"`
	Error   string `json:"error,omitempty"`
}

// String 返回 ToolResult 的字符串表示
func (tr *ToolResult) String() string {
	if tr == nil {
		return "ToolResult: nil"
	}

	if tr.Success {
		return fmt.Sprintf("ToolResult: Success=true, Content=%s", tr.Content)
	} else {
		return fmt.Sprintf("ToolResult: Success=false, Error=%s", tr.Error)
	}
}

// Message 消息結構
type Message struct {
	Role       string `json:"role"` // user, assistant, tool
	Content    string `json:"content"`
	ToolCallID string `json:"tool_call_id,omitempty"`
}

// ToolInfo 工具信息結構
type ToolInfo struct {
	Name        string                   `json:"name"`
	Description string                   `json:"description"`
	Parameters  map[string]ParameterInfo `json:"parameters"`
	ClientName  string                   `json:"client_name"`
}

// ParameterInfo 參數信息
type ParameterInfo struct {
	Type        string `json:"type"`
	Description string `json:"description"`
	Required    bool   `json:"required"`
}

// ToolCallHelper 工具調用輔助函數
type ToolCallHelper struct {
	logger glog.ILogger
}

// NewToolCallHelper 創建工具調用輔助器
func NewToolCallHelper() *ToolCallHelper {
	return &ToolCallHelper{
		logger: g.Log().Cat("ToolCall"),
	}
}

// BuildToolAnalysisPrompt 構建工具分析的 prompt
func (h *ToolCallHelper) BuildToolAnalysisPrompt(ctx context.Context, userMessage string, availableTools []ToolDefinition) string {
	// 構建工具描述
	toolDescriptions := h.buildToolDescriptions(availableTools)

	prompt := fmt.Sprintf(`你是一個智能助手，需要分析用戶請求是否需要使用外部工具。

可用工具：
%s

用戶請求："%s"

請分析這個請求並以 JSON 格式回答：
{
    "needs_tools": true/false,
    "reasoning": "分析原因",
    "suggested_tools": [
        {
            "tool_name": "工具名稱",
            "parameters": {
                "param1": "value1",
                "param2": "value2"
            },
            "reason": "使用此工具的原因"
        }
    ]
}

分析原則：
1. 如果用戶請求需要獲取實時信息、操作文件、執行計算等，則需要工具
2. 如果是純粹的對話、解釋、創作等，則不需要工具
3. 優先選擇最合適的工具，避免過度使用
4. 確保參數準確且完整

請只返回 JSON，不要其他內容。`, toolDescriptions, userMessage)

	return prompt
}

// buildToolDescriptions 構建工具描述字符串
func (h *ToolCallHelper) buildToolDescriptions(tools []ToolDefinition) string {
	if len(tools) == 0 {
		return "無可用工具"
	}

	var descriptions []string
	for _, tool := range tools {
		desc := fmt.Sprintf("- %s: %s", tool.Name, tool.Description)

		// 添加參數信息
		if tool.Parameters != nil {
			if properties, ok := tool.Parameters["properties"].(map[string]interface{}); ok {
				var params []string
				for paramName, paramInfo := range properties {
					if paramMap, ok := paramInfo.(map[string]interface{}); ok {
						paramType := gconv.String(paramMap["type"])
						paramDesc := gconv.String(paramMap["description"])
						params = append(params, fmt.Sprintf("%s(%s): %s", paramName, paramType, paramDesc))
					}
				}
				if len(params) > 0 {
					desc += fmt.Sprintf("\n  參數: %s", strings.Join(params, ", "))
				}
			}
		}

		descriptions = append(descriptions, desc)
	}

	return strings.Join(descriptions, "\n")
}

// ParseToolAnalysisResult 解析工具分析結果
func (h *ToolCallHelper) ParseToolAnalysisResult(ctx context.Context, responseText string) (*ToolAnalysisResult, error) {
	// 清理響應文本，移除可能的 markdown 標記
	responseText = strings.TrimSpace(responseText)
	responseText = strings.TrimPrefix(responseText, "```json")
	responseText = strings.TrimPrefix(responseText, "```")
	responseText = strings.TrimSuffix(responseText, "```")
	responseText = strings.TrimSpace(responseText)

	var result ToolAnalysisResult
	if err := gjson.New(responseText).Scan(&result); err != nil {
		h.logger.Errorf(ctx, "Failed to parse tool analysis result: %v, response: %s", err, responseText)
		return &ToolAnalysisResult{NeedsTools: false}, err
	}

	return &result, nil
}

// EnhanceMessageWithToolResults 將工具結果整合到消息中
func (h *ToolCallHelper) EnhanceMessageWithToolResults(originalMessage string, toolResults map[string]*ToolResult) string {
	// 構建工具結果的上下文
	var toolContext []string
	toolContext = append(toolContext, "=== 工具調用結果 ===")

	for toolName, result := range toolResults {
		if result.Success {
			toolContext = append(toolContext, fmt.Sprintf("工具 %s 執行成功：", toolName))
			toolContext = append(toolContext, result.Content)
		} else {
			toolContext = append(toolContext, fmt.Sprintf("工具 %s 執行失敗：%s", toolName, result.Error))
		}
		toolContext = append(toolContext, "---")
	}

	toolContext = append(toolContext, "=== 原始用戶請求 ===")
	toolContext = append(toolContext, originalMessage)
	toolContext = append(toolContext, "")
	toolContext = append(toolContext, "請基於以上工具調用結果回答用戶的問題。如果工具調用失敗，請說明情況並提供替代建議。")

	return strings.Join(toolContext, "\n")
}

// ValidateToolArguments 驗證工具參數
func (h *ToolCallHelper) ValidateToolArguments(ctx context.Context, toolDef ToolDefinition, args map[string]interface{}) error {
	if toolDef.Parameters == nil {
		return nil
	}

	// 檢查必需參數
	if required, ok := toolDef.Parameters["required"].([]interface{}); ok {
		for _, reqParam := range required {
			paramName := gconv.String(reqParam)
			if _, exists := args[paramName]; !exists {
				return fmt.Errorf("missing required parameter: %s", paramName)
			}
		}
	}

	// 檢查參數類型（簡單驗證）
	if properties, ok := toolDef.Parameters["properties"].(map[string]interface{}); ok {
		for paramName, value := range args {
			if paramInfo, exists := properties[paramName]; exists {
				if paramMap, ok := paramInfo.(map[string]interface{}); ok {
					expectedType := gconv.String(paramMap["type"])
					if !h.validateParameterType(value, expectedType) {
						return fmt.Errorf("parameter %s has invalid type, expected %s", paramName, expectedType)
					}
				}
			}
		}
	}

	return nil
}

// FormatToolResult 格式化工具結果為字符串
func (h *ToolCallHelper) FormatToolResult(result *ToolResult) string {
	if result == nil {
		return "ToolResult: nil"
	}

	if result.Success {
		return fmt.Sprintf("ToolResult: Success=true, Content=%s", result.Content)
	} else {
		return fmt.Sprintf("ToolResult: Success=false, Error=%s", result.Error)
	}
}

// ParseToolName 解析工具名稱，返回客戶端名稱和工具名稱
func (h *ToolCallHelper) ParseToolName(fullName string) (clientName, toolName string, err error) {
	if fullName == "" {
		return "", "", fmt.Errorf("tool name cannot be empty")
	}

	parts := strings.Split(fullName, ".")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid tool name format, expected 'client.tool', got '%s'", fullName)
	}

	clientName = strings.TrimSpace(parts[0])
	toolName = strings.TrimSpace(parts[1])

	if clientName == "" || toolName == "" {
		return "", "", fmt.Errorf("client name and tool name cannot be empty")
	}

	return clientName, toolName, nil
}

// CreateToolInfo 創建工具信息
func (h *ToolCallHelper) CreateToolInfo(toolDef ToolDefinition) ToolInfo {
	info := ToolInfo{
		Name:        toolDef.Name,
		Description: toolDef.Description,
		ClientName:  toolDef.ClientName,
		Parameters:  make(map[string]ParameterInfo),
	}

	// 解析參數信息
	if toolDef.Parameters != nil {
		if properties, ok := toolDef.Parameters["properties"].(map[string]interface{}); ok {
			for paramName, paramInfo := range properties {
				if paramMap, ok := paramInfo.(map[string]interface{}); ok {
					info.Parameters[paramName] = ParameterInfo{
						Type:        getStringValue(paramMap, "type"),
						Description: getStringValue(paramMap, "description"),
						Required:    h.isRequiredParam(toolDef.Parameters, paramName),
					}
				}
			}
		}
	}

	return info
}

// ValidateParameterType 驗證參數類型（公開方法）
func (h *ToolCallHelper) ValidateParameterType(paramType string, value interface{}) error {
	if h.validateParameterType(value, paramType) {
		return nil
	}
	return fmt.Errorf("parameter type validation failed: expected %s, got %T", paramType, value)
}

// getStringValue 從 map 中獲取字符串值
func getStringValue(m map[string]interface{}, key string) string {
	if v, ok := m[key]; ok {
		if s, ok := v.(string); ok {
			return s
		}
		return fmt.Sprintf("%v", v)
	}
	return ""
}

// isRequiredParam 檢查參數是否為必需
func (h *ToolCallHelper) isRequiredParam(schema map[string]interface{}, paramName string) bool {
	if required, ok := schema["required"].([]interface{}); ok {
		for _, req := range required {
			if reqStr, ok := req.(string); ok && reqStr == paramName {
				return true
			}
		}
	}
	return false
}

// validateParameterType 驗證參數類型
func (h *ToolCallHelper) validateParameterType(value interface{}, expectedType string) bool {
	switch expectedType {
	case "string":
		_, ok := value.(string)
		return ok
	case "integer":
		switch value.(type) {
		case int, int32, int64:
			return true
		default:
			return false
		}
	case "number":
		switch value.(type) {
		case int, int32, int64, float32, float64:
			return true
		default:
			return false
		}
	case "boolean":
		_, ok := value.(bool)
		return ok
	case "array":
		_, ok := value.([]interface{})
		return ok
	case "object":
		_, ok := value.(map[string]interface{})
		return ok
	default:
		return true // 未知類型，允許通過
	}
}

// ToolAnalysisResult 工具分析結果
type ToolAnalysisResult struct {
	NeedsTools     bool            `json:"needs_tools"`
	Reasoning      string          `json:"reasoning"`
	SuggestedTools []SuggestedTool `json:"suggested_tools"`
}

// SuggestedTool 建議的工具調用
type SuggestedTool struct {
	ToolName   string                 `json:"tool_name"`
	Parameters map[string]interface{} `json:"parameters"`
	Reason     string                 `json:"reason"`
}

// RetryConfig 重試配置
type RetryConfig struct {
	MaxAttempts int
	BaseDelay   time.Duration
	MaxDelay    time.Duration
}

// DefaultRetryConfig 默認重試配置
func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		MaxAttempts: 3,
		BaseDelay:   time.Second,
		MaxDelay:    time.Minute,
	}
}

// WithRetry 帶重試的執行函數
func WithRetry[T any](ctx context.Context, config RetryConfig, fn func() (T, error)) (T, error) {
	var result T
	var lastErr error

	for attempt := 1; attempt <= config.MaxAttempts; attempt++ {
		result, lastErr = fn()
		if lastErr == nil {
			return result, nil
		}

		// 如果是最後一次嘗試，直接返回錯誤
		if attempt == config.MaxAttempts {
			break
		}

		// 計算延遲時間（指數退避）
		delay := time.Duration(attempt) * config.BaseDelay
		if delay > config.MaxDelay {
			delay = config.MaxDelay
		}

		select {
		case <-ctx.Done():
			return result, ctx.Err()
		case <-time.After(delay):
			// 繼續下一次嘗試
		}
	}

	return result, lastErr
}
