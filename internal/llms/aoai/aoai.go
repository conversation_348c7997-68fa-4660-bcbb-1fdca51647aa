package aoai

import (
	"brainHub/internal/consts"
	"brainHub/internal/llms"
	"brainHub/internal/llms/common"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"brainHub/utility"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"

	"github.com/gabriel-vasile/mimetype"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/pkoukk/tiktoken-go"
	langchainllms "github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/openai"
)

// TokenCache Token 計算結果緩存結構
type TokenCache struct {
	count     int          // 緩存的 token 數量
	timestamp time.Time    // 緩存時間戳
	mutex     sync.RWMutex // 緩存讀寫鎖
}

// AoAi Azure OpenAI 服務實現
// 提供安全、高效能的 AI 對話功能
type AoAi struct {
	llm             *openai.LLM   // OpenAI LLM 實例
	temperature     float32       // 生成溫度參數
	maxOutputTokens int           // 最大輸出 token 數量
	modelName       string        // 模型名稱
	history         *garray.Array // 對話歷史記錄（線程安全）
	tokenCache      *TokenCache   // Token 計算結果緩存
	payload         *llm.Payload  // 儲存初始化時的 payload 資料，用於重新載入和 token 管理

}

// logger 返回專用的日誌記錄器
func (s *AoAi) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogAoAi)
}

// logConfigSafely 安全地記錄配置資訊，避免洩露敏感資料
func (s *AoAi) logConfigSafely(ctx context.Context, params *llm.LLMsConfig) {
	if params == nil || params.AoAi == nil {
		return
	}

	// 創建一個不包含敏感資訊的副本用於日誌記錄
	safeConfig := *params.AoAi
	if !g.IsEmpty(safeConfig.Token) {
		safeConfig.Token = "***masked***"
	}

	s.logger().Debugf(ctx, "Initialize with parameter: %v", gjson.New(safeConfig).MustToJsonIndentString())
}

// Initialize 初始化 Azure OpenAI 服務
// 包含完整的參數驗證和錯誤處理
func (s *AoAi) Initialize(ctx context.Context, params *llm.LLMsConfig, payload *llm.Payload) (err error) {
	// 安全地記錄配置資訊
	s.logConfigSafely(ctx, params)

	// 參數驗證
	if params == nil {
		err = gerror.New("the params is nil")
		s.logger().Error(ctx, err)
		return
	}
	if params.AoAi == nil {
		err = gerror.New("the aoai params is nil")
		s.logger().Error(ctx, err)
		return
	}

	// 驗證必要的配置參數
	if g.IsEmpty(params.AoAi.BaseUrl) {
		err = gerror.New("aoai base url is required")
		s.logger().Error(ctx, err)
		return
	}
	if g.IsEmpty(params.AoAi.Token) {
		err = gerror.New("aoai token is required")
		s.logger().Error(ctx, err)
		return
	}
	if g.IsEmpty(params.AoAi.ModelId) {
		err = gerror.New("aoai model id is required")
		s.logger().Error(ctx, err)
		return
	}

	// 創建 OpenAI LLM 實例
	s.llm, err = openai.New(
		openai.WithBaseURL(params.AoAi.BaseUrl),
		openai.WithAPIType(openai.APITypeAzure),
		openai.WithAPIVersion(params.AoAi.APIVersion),
		openai.WithModel(params.AoAi.ModelId),
		openai.WithToken(params.AoAi.Token),
	)
	if err != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, err, "Failed to create OpenAI LLM instance")
		s.logger().Error(ctx, wrappedErr)
		return wrappedErr
	}

	// 設置配置參數
	s.temperature = params.AoAi.Temperature
	s.maxOutputTokens = params.AoAi.MaxToken
	s.modelName = params.AoAi.ModelId

	// 初始化對話歷史記錄（線程安全）
	s.history = garray.New(true) // true 表示啟用併發安全

	// 初始化 Token 緩存
	s.tokenCache = &TokenCache{
		count:     consts.DBResultEmpty,
		timestamp: time.Now(),
	}

	// 儲存 payload 以供後續使用（如 token 管理時的重新載入）
	s.payload = payload

	// 創建新的對話會話
	err = s.createNewChat(ctx, payload, "")
	if err != nil {
		s.logger().Error(ctx, "Failed to create new chat during initialization:", err)
		return err
	}

	s.logger().Info(ctx, "Azure OpenAI service initialized successfully")

	return nil
}

// createNewChat 創建新的對話會話
// 支援系統指令和對話摘要的初始化
func (s *AoAi) createNewChat(ctx context.Context, payload *llm.Payload, dialogSummary string) (err error) {
	s.logger().Debugf(ctx, "Create new chat with payload: %v, summary: %v",
		gjson.New(payload).MustToJsonIndentString(), dialogSummary)

	// 清空現有歷史記錄
	s.history.Clear()

	// 添加系統指令（如果存在）
	if payload != nil && !g.IsEmpty(payload.SystemInstruction) {
		s.history.Append(langchainllms.MessageContent{
			Role: langchainllms.ChatMessageTypeSystem,
			Parts: []langchainllms.ContentPart{
				langchainllms.TextPart(payload.SystemInstruction),
			},
		})
		s.logger().Debug(ctx, "Added system instruction to chat history")
	}

	// 處理附件內容
	if payload != nil && payload.Attachments != nil {
		err = s.processAttachments(ctx, payload.Attachments)
		if err != nil {
			s.logger().Error(ctx, "Failed to process attachments:", err)
			return err
		}
	}

	// 處理對話摘要和歷史記錄
	if !g.IsEmpty(dialogSummary) {
		// 如果有對話摘要，直接使用
		s.history.Append(langchainllms.MessageContent{
			Role: langchainllms.ChatMessageTypeHuman,
			Parts: []langchainllms.ContentPart{
				langchainllms.TextPart(dialogSummary),
			},
		})
		s.logger().Debug(ctx, "Added dialog summary to chat history")
	} else if payload != nil && payload.History != nil {
		// 如果沒有對話摘要但有歷史記錄，則對歷史記錄進行總結
		// 先將歷史記錄轉換為字符串數組檢查是否有內容
		historyStrings := gconv.Strings(payload.History)
		if len(historyStrings) > 0 {
			s.logger().Debug(ctx, "No dialog summary provided, processing payload history")

			// 將歷史記錄轉換為對話格式並進行總結
			err = s.processPayloadHistory(ctx, payload.History)
			if err != nil {
				s.logger().Error(ctx, "Failed to process payload history:", err)
				return err
			}
		}
	}

	return nil
}

// processPayloadHistory 處理 payload 中的歷史記錄並進行總結
// 將歷史記錄轉換為對話格式，然後生成總結並添加到當前對話中
func (s *AoAi) processPayloadHistory(ctx context.Context, history interface{}) error {
	s.logger().Debug(ctx, "Processing payload history for summarization")

	// 檢查 LLM 實例是否可用
	if s.llm == nil {
		s.logger().Warning(ctx, "LLM instance not available, skipping history processing")
		return nil
	}

	// 將歷史記錄轉換為字符串數組
	historyStrings := gconv.Strings(history)
	if len(historyStrings) == 0 {
		s.logger().Debug(ctx, "No valid history strings found")
		return nil
	}

	s.logger().Debugf(ctx, "Found %d history items to process", len(historyStrings))

	// 限制歷史記錄數量以避免性能問題
	maxHistoryItems := 10
	if len(historyStrings) > maxHistoryItems {
		s.logger().Warningf(ctx, "Limiting history processing to %d items (found %d)", maxHistoryItems, len(historyStrings))
		historyStrings = historyStrings[:maxHistoryItems]
	}

	// 構建臨時對話歷史用於總結
	tempHistory := make([]langchainllms.MessageContent, 0, len(historyStrings)+1)

	// 將歷史記錄添加為用戶消息
	for i, historyItem := range historyStrings {
		if !g.IsEmpty(historyItem) {
			// 限制單個歷史項目的長度
			if len(historyItem) > 1000 {
				historyItem = historyItem[:1000] + "..."
			}

			tempHistory = append(tempHistory, langchainllms.MessageContent{
				Role: langchainllms.ChatMessageTypeHuman,
				Parts: []langchainllms.ContentPart{
					langchainllms.TextPart(fmt.Sprintf("Previous conversation #%d: %s", i+1, historyItem)),
				},
			})
		}
	}

	if len(tempHistory) == 0 {
		s.logger().Debug(ctx, "No valid history items to process after filtering")
		return nil
	}

	// 添加總結提示
	tempHistory = append(tempHistory, langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeHuman,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart("Please summarize the above conversation history in a concise manner, focusing on the key points and context."),
		},
	})

	s.logger().Debugf(ctx, "Generating summary for %d history items", len(tempHistory)-1)

	// 生成總結，添加超時保護
	resp, err := s.llm.GenerateContent(
		ctx,
		tempHistory,
		langchainllms.WithTemperature(consts.AoAiSummaryTemp), // 使用較低的溫度
		langchainllms.WithMaxTokens(s.maxOutputTokens/4),      // 限制總結長度
	)

	if err != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, err, "Failed to generate history summary")
		s.logger().Error(ctx, wrappedErr)
		return wrappedErr
	}

	// 提取總結文本
	var summary string
	if len(resp.Choices) > 0 && len(resp.Choices[0].Content) > 0 {
		summary = resp.Choices[0].Content
		s.logger().Debugf(ctx, "Generated history summary: %s", summary)
	} else {
		err = gerror.New("Failed to generate history summary: empty response from API")
		s.logger().Error(ctx, err)
		return err
	}

	// 將總結添加到當前對話歷史
	s.history.Append(langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeHuman,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart(fmt.Sprintf("Previous conversation summary: %s", summary)),
		},
	})

	s.logger().Info(ctx, "Successfully processed and summarized payload history")
	return nil
}

// processAttachments 處理附件內容
// 支援文件、網頁內容和純文本，將文件轉換為文本格式處理
func (s *AoAi) processAttachments(ctx context.Context, attachments *model.Asset) error {
	s.logger().Info(ctx, "=== START process Attachments ===")

	// nil 檢查
	if attachments == nil {
		s.logger().Info(ctx, "No attachments to process (nil)")
		return nil
	}

	// 檢查附件數量
	webPageCount := len(attachments.WebPageFiles)
	filesCount := len(attachments.Files)
	plainTextCount := len(attachments.PlainText)

	s.logger().Infof(ctx, "Attachment counts: WebPages=%d, Files=%d, PlainText=%d",
		webPageCount, filesCount, plainTextCount)

	// 設置處理限制，避免處理過多附件
	maxAttachments := 10
	totalAttachments := webPageCount + filesCount + plainTextCount

	if totalAttachments > maxAttachments {
		s.logger().Warningf(ctx, "Too many attachments (%d), limiting to %d",
			totalAttachments, maxAttachments)
	}

	// Token 管理檢查
	s.logger().Debug(ctx, "Checking token count before processing attachments...")
	err := s.checkAndManageTokens(ctx)
	if err != nil {
		s.logger().Errorf(ctx, "Failed to check tokens: %v", err)
		return gerror.Wrap(err, "failed to check and manage tokens before processing attachments")
	}
	s.logger().Debug(ctx, "Token check completed")

	processedCount := 0
	skippedCount := 0

	// 設置處理開始時間，用於超時檢測
	startTime := time.Now()

	// 1. 處理 WebPageFiles (網頁內容文件)
	s.logger().Infof(ctx, "Processing %d Web Page files...", len(attachments.WebPageFiles))
	for idx, webPageFile := range attachments.WebPageFiles {
		s.logger().Debugf(ctx, "Processing Web Page file %d/%d: %s", idx+1, len(attachments.WebPageFiles), webPageFile)

		// 檢查超時
		if time.Since(startTime) > 30*time.Second {
			s.logger().Warningf(ctx, "Attachment processing timeout after 30 seconds, stopping")
			break
		}

		if g.IsEmpty(webPageFile) {
			s.logger().Debug(ctx, "Skipping empty Web Page file")
			continue
		}

		// 文件驗證和安全檢查
		if !gfile.Exists(webPageFile) {
			s.logger().Warningf(ctx, "Web Page file does not exist: %s", webPageFile)
			skippedCount++
			continue
		}
		if gfile.Size(webPageFile) == 0 {
			s.logger().Warningf(ctx, "Web Page file is empty: %s", webPageFile)
			skippedCount++
			continue
		}
		if gfile.Size(webPageFile) > 10*1024*1024 { // 10MB 限制
			s.logger().Warningf(ctx, "Web Page file too large (>10MB): %s", webPageFile)
			skippedCount++
			continue
		}

		// 讀取網頁內容文件
		content := gfile.GetContents(webPageFile)
		if g.IsEmpty(content) {
			s.logger().Warningf(ctx, "Failed to read Web Page file content: %s", webPageFile)
			skippedCount++
			continue
		}

		// 添加到對話歷史（garray 自動處理線程安全）
		s.history.Append(langchainllms.MessageContent{
			Role: langchainllms.ChatMessageTypeHuman,
			Parts: []langchainllms.ContentPart{
				langchainllms.TextPart(fmt.Sprintf("網頁內容文件 (%s):\n%s", gfile.Basename(webPageFile), content)),
			},
		})

		// 使 Token 緩存失效
		s.invalidateTokenCache(ctx)
		processedCount++

		s.logger().Debugf(ctx, "Processed Web Page file: %s", webPageFile)

		// 每處理指定數量的文件檢查一次 token 數量，避免頻繁計算
		if processedCount%consts.AoAiAttachmentCheckInterval == 0 {
			tokenCount, tokenErr := s.calculateTokenCount(ctx)
			if tokenErr == nil && tokenCount > int(float64(consts.AoAiTokenThreshold)*consts.AoAiTokenCheckThreshold) {
				s.logger().Warningf(ctx, "Token count (%d) approaching threshold (%d), stopping attachment processing",
					tokenCount, int(float64(consts.AoAiTokenThreshold)*consts.AoAiTokenCheckThreshold))
				break
			}
		}
	}

	// 2. 處理 Files (文件附件)
	s.logger().Infof(ctx, "Processing %d Files...", len(attachments.Files))
	for idx, filePath := range attachments.Files {
		s.logger().Debugf(ctx, "Processing File %d/%d: %s", idx+1, len(attachments.Files), filePath)

		// 檢查超時
		if time.Since(startTime) > 30*time.Second {
			s.logger().Warningf(ctx, "Attachment processing timeout after 30 seconds, stopping")
			break
		}

		if g.IsEmpty(filePath) {
			s.logger().Debug(ctx, "Skipping empty file path")
			continue
		}

		// 文件驗證和安全檢查
		if !gfile.Exists(filePath) {
			s.logger().Warningf(ctx, "File does not exist: %s", filePath)
			skippedCount++
			continue
		}
		if gfile.Size(filePath) == 0 {
			s.logger().Warningf(ctx, "File is empty: %s", filePath)
			skippedCount++
			continue
		}
		if gfile.Size(filePath) > 10*1024*1024 { // 10MB 限制
			s.logger().Warningf(ctx, "File too large (>10MB): %s", filePath)
			skippedCount++
			continue
		}

		// 檢測 MIME 類型
		mimeType, err := mimetype.DetectFile(filePath)
		if err != nil {
			s.logger().Warningf(ctx, "Failed to detect MIME type for file %s: %v", filePath, err)
			skippedCount++
			continue
		}

		mimeTypeStr := mimeType.String()

		// 嘗試使用 markitdown 轉換文件
		convertedPath, isConverted, shouldSkip, convErr := utility.ConvertFileToMarkdown(ctx, filePath, mimeTypeStr)
		if convErr != nil || shouldSkip {
			if convErr != nil {
				s.logger().Errorf(ctx, "Failed to convert file %s to markdown, skipping file: %v", filePath, convErr)
			}
			skippedCount++
			continue
		}

		// 讀取文件內容（轉換後的或原始的）
		content := gfile.GetContents(convertedPath)
		if g.IsEmpty(content) {
			s.logger().Warningf(ctx, "Failed to read file content or file is empty: %s", convertedPath)
			skippedCount++
			// 如果轉換成功但讀取失敗，清理臨時文件
			if isConverted && convertedPath != filePath {
				_ = gfile.RemoveFile(convertedPath)
			}
			continue
		}

		// 創建文件內容描述
		fileTypeDesc := mimeTypeStr
		if isConverted {
			fileTypeDesc = "markdown (converted from " + mimeTypeStr + ")"
		}

		// 添加到對話歷史（garray 自動處理線程安全）
		s.history.Append(langchainllms.MessageContent{
			Role: langchainllms.ChatMessageTypeHuman,
			Parts: []langchainllms.ContentPart{
				langchainllms.TextPart(fmt.Sprintf("文件內容 (%s, MIME: %s):\n%s", gfile.Basename(filePath), fileTypeDesc, content)),
			},
		})

		// 使 Token 緩存失效
		s.invalidateTokenCache(ctx)
		processedCount++

		s.logger().Debugf(ctx, "Processed file: %s (MIME: %s, converted: %v)", filePath, mimeTypeStr, isConverted)

		// 如果轉換成功，清理臨時文件
		if isConverted && convertedPath != filePath {
			_ = gfile.RemoveFile(convertedPath)
		}

		// 每處理指定數量的文件檢查一次 token 數量，避免頻繁計算
		if processedCount%consts.AoAiAttachmentCheckInterval == 0 {
			tokenCount, tokenErr := s.calculateTokenCount(ctx)
			if tokenErr == nil && tokenCount > int(float64(consts.AoAiTokenThreshold)*consts.AoAiTokenCheckThreshold) {
				s.logger().Warningf(ctx, "Token count (%d) approaching threshold (%d), stopping attachment processing",
					tokenCount, int(float64(consts.AoAiTokenThreshold)*consts.AoAiTokenCheckThreshold))
				break
			}
		}
	}

	// 3. 處理 PlainText (純文本內容)
	s.logger().Infof(ctx, "Processing %d Plain Text items...", len(attachments.PlainText))
	for i, plainText := range attachments.PlainText {
		s.logger().Debugf(ctx, "Processing Plain Text %d/%d (length: %d)", i+1, len(attachments.PlainText), len(plainText))

		// 檢查超時
		if time.Since(startTime) > 30*time.Second {
			s.logger().Warningf(ctx, "Attachment processing timeout after 30 seconds, stopping")
			break
		}

		if g.IsEmpty(plainText) {
			s.logger().Debug(ctx, "Skipping empty plain text")
			continue
		}

		// 添加到對話歷史（garray 自動處理線程安全）
		s.history.Append(langchainllms.MessageContent{
			Role: langchainllms.ChatMessageTypeHuman,
			Parts: []langchainllms.ContentPart{
				langchainllms.TextPart(fmt.Sprintf("純文本內容 #%d:\n%s", i+1, plainText)),
			},
		})

		// 使 Token 緩存失效
		s.invalidateTokenCache(ctx)
		processedCount++

		s.logger().Debugf(ctx, "Processed plain text #%d", i+1)

		// 每處理指定數量的純文本檢查一次 token 數量，避免頻繁計算
		if processedCount%consts.AoAiAttachmentCheckInterval == 0 {
			tokenCount, tokenErr := s.calculateTokenCount(ctx)
			if tokenErr == nil && tokenCount > int(float64(consts.AoAiTokenThreshold)*consts.AoAiTokenCheckThreshold) {
				s.logger().Warningf(ctx, "Token count (%d) approaching threshold (%d), stopping attachment processing",
					tokenCount, int(float64(consts.AoAiTokenThreshold)*consts.AoAiTokenCheckThreshold))
				break
			}
		}
	}

	duration := time.Since(startTime)
	s.logger().Infof(ctx, "=== COMPLETED process Attachments: processed=%d, skipped=%d, duration=%v ===",
		processedCount, skippedCount, duration)
	return nil
}

// getResponse 處理用戶消息並生成 AI 回應
// 包含重試機制和完整的錯誤處理
func (s *AoAi) getResponse(ctx context.Context, userMessage string) (aiResponse string, err error) {
	s.logger().Debugf(ctx, "Start processing user message: %s", userMessage)

	// 參數驗證
	if g.IsEmpty(userMessage) {
		err = gerror.New("user message cannot be empty")
		s.logger().Error(ctx, err)
		return "", err
	}

	// 添加用戶消息到歷史記錄（garray 自動處理線程安全）
	s.history.Append(langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeHuman,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart(userMessage),
		},
	})

	// 創建歷史記錄的副本用於 API 調用
	historySlice := s.history.Slice()
	historyCopy := make([]langchainllms.MessageContent, len(historySlice))
	for i, item := range historySlice {
		historyCopy[i] = item.(langchainllms.MessageContent)
	}

	// 使 Token 緩存失效，因為歷史記錄已更改
	s.invalidateTokenCache(ctx)

	// 實現重試機制
	var resp *langchainllms.ContentResponse
	var lastErr error

	for attempt := consts.CacheExistsTrue; attempt <= consts.AoAiMaxRetryAttempts; attempt++ {
		// 使用 LLM 生成內容
		resp, lastErr = s.llm.GenerateContent(
			ctx,
			historyCopy,
			langchainllms.WithTemperature(gconv.Float64(s.temperature)),
			langchainllms.WithMaxTokens(s.maxOutputTokens),
		)

		if lastErr == nil {
			// 請求成功，跳出重試循環
			s.logger().Debugf(ctx, "API call succeeded on attempt %d", attempt)
			break
		}

		// 記錄錯誤並準備重試
		s.logger().Infof(ctx, "API call failed, attempt %d/%d: %v",
			attempt, consts.AoAiMaxRetryAttempts, lastErr)

		// 如果不是最後一次嘗試，則等待後重試
		if attempt < consts.AoAiMaxRetryAttempts {
			vTtl, _ := g.Cfg().Get(ctx, consts.ConfigSystemAISendRetryTTL, consts.TimeFormatDuration40Sec)
			time.Sleep(vTtl.Duration())
		}
	}

	// 如果所有嘗試都失敗，返回包裝後的錯誤
	if lastErr != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, lastErr,
			fmt.Sprintf("Failed to generate response after %d attempts", consts.AoAiMaxRetryAttempts))
		s.logger().Error(ctx, wrappedErr)
		return "", wrappedErr
	}

	// 從響應中提取 AI 回覆文本
	if resp != nil && len(resp.Choices) > 0 && len(resp.Choices[0].Content) > 0 {
		aiResponse = resp.Choices[0].Content

		// 將 AI 回覆添加到歷史記錄（garray 自動處理線程安全）
		s.history.Append(langchainllms.MessageContent{
			Role: langchainllms.ChatMessageTypeAI,
			Parts: []langchainllms.ContentPart{
				langchainllms.TextPart(aiResponse),
			},
		})

		// 使 Token 緩存失效，因為歷史記錄已更改
		s.invalidateTokenCache(ctx)

		s.logger().Debugf(ctx, "Generated response: %s", aiResponse)
	} else {
		err = gerror.New("Failed to generate response: empty response from API")
		s.logger().Error(ctx, err)
		return "", err
	}

	return aiResponse, nil
}

// getResponseWithTools 支持工具調用的響應生成
func (s *AoAi) getResponseWithTools(ctx context.Context, userMessage string) (string, error) {
	s.logger().Infof(ctx, "=== START get Response With Tools ===")
	s.logger().Debugf(ctx, "Processing message with tool support: %s", userMessage)

	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		s.logger().Infof(ctx, "=== END get Response With Tools === Duration: %v", duration)
	}()

	// 工具定義功能已移除
	s.logger().Debugf(ctx, "Step 1: Tool definitions disabled (MCP removed)")
	var toolDefinitions []common.ToolDefinition

	// 添加用戶消息到歷史
	s.logger().Debugf(ctx, "Step 2: Adding user message to history...")
	s.history.Append(langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeHuman,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart(userMessage),
		},
	})
	s.logger().Debugf(ctx, "User message added to history successfully")

	// 準備 LLM 調用
	s.logger().Debugf(ctx, "Step 3: Preparing LLM call options...")
	historyCopy := s.createHistoryCopy()
	s.logger().Debugf(ctx, "History copy created with %d messages", len(historyCopy))

	options := []langchainllms.CallOption{
		langchainllms.WithTemperature(gconv.Float64(s.temperature)),
		langchainllms.WithMaxTokens(s.maxOutputTokens),
	}
	s.logger().Debugf(ctx, "Base options set: temperature=%.2f, maxTokens=%d", s.temperature, s.maxOutputTokens)

	// 如果有工具定義，添加到選項中
	if len(toolDefinitions) > 0 {
		s.logger().Debugf(ctx, "Converting %d tool definitions to OpenAI format...", len(toolDefinitions))
		tools := s.convertToOpenAITools(toolDefinitions)
		options = append(options, langchainllms.WithTools(tools))
		s.logger().Infof(ctx, "Tools added to LLM options: %d tools configured", len(tools))
	} else {
		s.logger().Debugf(ctx, "No tools to add to LLM options")
	}

	// 調用 LLM
	s.logger().Infof(ctx, "Step 4: Calling LLM with retry mechanism...")
	resp, err := s.callLLMWithRetry(ctx, historyCopy, options)
	if err != nil {
		s.logger().Error(ctx, "LLM call failed:", err)
		return "", err
	}
	s.logger().Infof(ctx, "LLM call completed successfully")

	// 處理響應（可能包含工具調用）
	s.logger().Infof(ctx, "Step 5: Processing LLM response...")
	result, err := s.processResponse(ctx, resp, toolDefinitions)
	if err != nil {
		s.logger().Error(ctx, "Response processing failed:", err)
		return "", err
	}
	s.logger().Infof(ctx, "Response processing completed successfully")

	return result, nil
}

// calculateTokenCount 計算對話歷史中的 token 數量
// 使用 tiktoken 庫進行精確計算，添加緩存機制和錯誤處理
func (s *AoAi) calculateTokenCount(ctx context.Context) (int, error) {
	s.logger().Debug(ctx, "=== START calculate Token Count ===")

	if s.llm == nil {
		s.logger().Error(ctx, "LLM instance is not initialized")
		return 0, gerror.New("LLM instance is not initialized")
	}
	s.logger().Debug(ctx, "LLM instance check passed")

	// 檢查緩存是否有效
	if s.tokenCache != nil {
		s.tokenCache.mutex.RLock()
		cacheAge := time.Since(s.tokenCache.timestamp).Seconds()
		if cacheAge < consts.AoAiTokenCacheTimeout {
			cachedCount := s.tokenCache.count
			s.tokenCache.mutex.RUnlock()
			s.logger().Debugf(ctx, "Using cached token count: %d (age: %.1fs)", cachedCount, cacheAge)
			return cachedCount, nil
		}
		s.tokenCache.mutex.RUnlock()
		s.logger().Debug(ctx, "Token cache expired, recalculating...")
	}

	// 獲取模型名稱，使用預設值作為後備
	modelName := s.modelName
	if g.IsEmpty(modelName) {
		modelName = consts.AoAiDefaultModel
		s.logger().Noticef(ctx, "Using default model name: %s", modelName)
	}
	s.logger().Debugf(ctx, "Model name: %s", modelName)

	// 使用 tiktoken-go 計算 token，添加錯誤處理
	s.logger().Debug(ctx, "Getting encoding for model...")
	encoding, err := tiktoken.EncodingForModel(modelName)
	if err != nil {
		// 如果找不到特定模型的編碼，使用預設編碼
		s.logger().Noticef(ctx, "Failed to get encoding for model %s: %v, using %s instead",
			modelName, err, consts.AoAiDefaultEncoding)
		s.logger().Debug(ctx, "Trying to get default encoding...")
		encoding, err = tiktoken.GetEncoding(consts.AoAiDefaultEncoding)
		if err != nil {
			wrappedErr := gerror.WrapCode(consts.ApiFailed, err, "Failed to initialize token encoding")
			s.logger().Error(ctx, wrappedErr)
			return 0, wrappedErr
		}
		s.logger().Debug(ctx, "Default encoding obtained successfully")
	} else {
		s.logger().Debug(ctx, "Model-specific encoding obtained successfully")
	}

	// 讀取歷史記錄（garray 自動處理線程安全）
	s.logger().Debug(ctx, "Reading history from garray...")
	historySlice := s.history.Slice()
	historyLen := len(historySlice)
	s.logger().Debugf(ctx, "History length: %d", historyLen)

	if historyLen == 0 {
		s.logger().Debug(ctx, "No conversation history, returning 0 tokens")
		return 0, nil
	}

	s.logger().Debug(ctx, "Creating history copy...")
	historyCopy := make([]langchainllms.MessageContent, historyLen)
	for i, item := range historySlice {
		historyCopy[i] = item.(langchainllms.MessageContent)
	}
	s.logger().Debug(ctx, "History copy created")

	// 計算對話歷史的 token 數量
	s.logger().Debug(ctx, "Starting token calculation...")
	totalTokens := consts.AoAiTokenOverhead // 每個請求的基本開銷
	s.logger().Debugf(ctx, "Initial token overhead: %d", totalTokens)

	for i, msg := range historyCopy {
		s.logger().Debugf(ctx, "Processing message %d/%d", i+1, historyLen)

		// 每條消息的基本開銷
		totalTokens += consts.AoAiMsgOverhead
		s.logger().Debugf(ctx, "Added message overhead, total tokens: %d", totalTokens)

		// 安全地計算角色的 token
		roleStr := string(msg.Role)
		s.logger().Debugf(ctx, "Message role: %s", roleStr)
		if !g.IsEmpty(roleStr) {
			s.logger().Debug(ctx, "Encoding role...")
			roleTokens := encoding.Encode(roleStr, nil, nil)
			totalTokens += len(roleTokens)
			s.logger().Debugf(ctx, "Role tokens: %d, total tokens: %d", len(roleTokens), totalTokens)
		}

		// 計算內容的 token，添加安全檢查
		s.logger().Debugf(ctx, "Processing %d content parts", len(msg.Parts))
		var msgBuilder strings.Builder
		for partIdx, part := range msg.Parts {
			s.logger().Debugf(ctx, "Processing part %d/%d", partIdx+1, len(msg.Parts))
			if part == nil {
				s.logger().Debug(ctx, "Skipping nil part")
				continue
			}

			// 安全地處理不同類型的內容部分
			var partText string
			switch p := part.(type) {
			case interface{ GetText() string }:
				s.logger().Debug(ctx, "Getting text from GetText() method")
				partText = p.GetText()
			case interface{ String() string }:
				s.logger().Debug(ctx, "Getting text from String() method")
				partText = p.String()
			default:
				s.logger().Debug(ctx, "Using fmt.Sprintf for text conversion")
				partText = fmt.Sprintf("%v", part)
			}

			s.logger().Debugf(ctx, "Part text length: %d", len(partText))

			// 限制單個部分的長度以避免性能問題
			if len(partText) > 10000 {
				partText = partText[:10000] + "..."
				s.logger().Warningf(ctx, "Message part truncated due to length in token calculation")
			}

			msgBuilder.WriteString(partText)
		}

		msgText := msgBuilder.String()
		s.logger().Debugf(ctx, "Complete message text length: %d", len(msgText))

		if !g.IsEmpty(msgText) {
			s.logger().Debug(ctx, "Encoding message content...")
			// 安全地編碼文本
			contentTokens := encoding.Encode(msgText, nil, nil)
			totalTokens += len(contentTokens)
			s.logger().Debugf(ctx, "Content tokens: %d, total tokens: %d", len(contentTokens), totalTokens)
		}

		// 每處理 5 條消息記錄一次進度
		if (i+1)%5 == 0 {
			s.logger().Debugf(ctx, "Processed %d/%d messages for token calculation, current total: %d", i+1, historyLen, totalTokens)
		}
	}

	// 更新緩存
	if s.tokenCache != nil {
		s.tokenCache.mutex.Lock()
		s.tokenCache.count = totalTokens
		s.tokenCache.timestamp = time.Now()
		s.tokenCache.mutex.Unlock()
		s.logger().Debug(ctx, "Token cache updated")
	}

	s.logger().Debugf(ctx, "=== COMPLETED calculate Token Count: %d tokens ===", totalTokens)
	return totalTokens, nil
}

// invalidateTokenCache 使 Token 緩存失效
// 當對話歷史發生變化時調用此方法
func (s *AoAi) invalidateTokenCache(ctx context.Context) {
	if s.tokenCache != nil {
		s.tokenCache.mutex.Lock()
		s.tokenCache.count = 0
		s.tokenCache.timestamp = time.Time{} // 設置為零值使緩存立即失效
		s.tokenCache.mutex.Unlock()
		s.logger().Debug(ctx, "Token cache invalidated")
	}
}

// summarizeConversation 總結對話歷史以減少 token 數量
// 保留系統指令並生成對話摘要
func (s *AoAi) summarizeConversation(ctx context.Context) (err error) {
	s.logger().Debug(ctx, "Summarizing conversation to reduce token count")

	// 第一步：讀取歷史記錄（garray 自動處理線程安全）
	historySlice := s.history.Slice()
	historyLen := len(historySlice)

	if historyLen < 3 {
		// 對話太短，不需要總結
		s.logger().Debug(ctx, "Conversation too short, no summarization needed")
		return nil
	}

	// 保留系統指令（如果存在）
	var systemInstruction langchainllms.MessageContent
	hasSystemInstruction := false

	if historyLen > 0 {
		firstItem := historySlice[0].(langchainllms.MessageContent)
		if firstItem.Role == langchainllms.ChatMessageTypeSystem {
			systemInstruction = firstItem
			hasSystemInstruction = true
			s.logger().Debug(ctx, "System instruction found and will be preserved")
		}
	}

	// 創建歷史記錄的副本用於總結
	historyCopy := make([]langchainllms.MessageContent, historyLen)
	for i, item := range historySlice {
		historyCopy[i] = item.(langchainllms.MessageContent)
	}

	// 第二步：在沒有鎖的情況下進行 API 調用
	// 創建總結提示
	summaryPrompt := "Please summarize our conversation so far in a concise manner, focusing on the key points and context."
	historyCopy = append(historyCopy, langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeHuman,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart(summaryPrompt),
		},
	})

	s.logger().Debug(ctx, "Calling API to generate conversation summary...")

	// 獲取總結
	resp, err := s.llm.GenerateContent(
		ctx,
		historyCopy,
		langchainllms.WithTemperature(consts.AoAiSummaryTemp), // 使用較低的溫度以獲得更確定性的總結
		langchainllms.WithMaxTokens(s.maxOutputTokens/4),      // 限制總結的長度
	)

	if err != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, err, "Failed to generate conversation summary")
		s.logger().Error(ctx, wrappedErr)
		return wrappedErr
	}

	// 提取總結文本
	var summary string
	if len(resp.Choices) > 0 && len(resp.Choices[0].Content) > 0 {
		summary = resp.Choices[0].Content
		s.logger().Debugf(ctx, "Generated summary: %s", summary)
	} else {
		err = gerror.New("Failed to generate summary: empty response from API")
		s.logger().Error(ctx, err)
		return err
	}

	// 第三步：更新歷史記錄（garray 自動處理線程安全）
	// 重置歷史記錄，只保留系統指令和總結
	s.history.Clear()

	if hasSystemInstruction {
		s.history.Append(systemInstruction)
	}

	// 添加總結作為用戶消息
	s.history.Append(langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeHuman,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart("Here's a summary of our conversation so far: " + summary),
		},
	})

	// 使 Token 緩存失效，因為歷史記錄已重置
	s.invalidateTokenCache(ctx)

	s.logger().Info(ctx, "Conversation successfully summarized and history reset")
	return nil
}

// checkAndManageTokens 檢查並管理 token 數量
// 當超過閾值時自動觸發對話總結
func (s *AoAi) checkAndManageTokens(ctx context.Context) error {
	tokenCount, err := s.calculateTokenCount(ctx)
	if err != nil {
		s.logger().Error(ctx, "Failed to calculate token count:", err)
		// 提供降級策略：如果 token 計算失敗，假設未超過閾值
		s.logger().Warning(ctx, "Using fallback strategy: assuming token count is within threshold")
		return nil
	}

	if tokenCount > consts.AoAiTokenThreshold {
		s.logger().Infof(ctx, "Token count (%d) exceeds threshold (%d), summarizing conversation",
			tokenCount, consts.AoAiTokenThreshold)
		return s.summarizeConversation(ctx)
	}

	s.logger().Debugf(ctx, "Token count (%d) is within threshold (%d)",
		tokenCount, consts.AoAiTokenThreshold)
	return nil
}

// checkAndManageTokensWithPayload 檢查並管理 token 數量，支援 payload 重新載入
// 當超過閾值時自動觸發對話總結並創建新對話會話
func (s *AoAi) checkAndManageTokensWithPayload(ctx context.Context, payload *llm.Payload) error {
	tokenCount, err := s.calculateTokenCount(ctx)
	if err != nil {
		s.logger().Error(ctx, "Failed to calculate token count:", err)
		// 提供降級策略：如果 token 計算失敗，假設未超過閾值
		s.logger().Warning(ctx, "Using fallback strategy: assuming token count is within threshold")
		return nil
	}

	if tokenCount > consts.AoAiTokenThreshold {
		s.logger().Infof(ctx, "Token count (%d) exceeds threshold (%d), summarizing and creating new conversation",
			tokenCount, consts.AoAiTokenThreshold)

		// 生成對話總結
		summary, err := s.generateConversationSummary(ctx)
		if err != nil {
			s.logger().Error(ctx, "Failed to generate conversation summary:", err)
			return err
		}

		// 優先使用傳入的 payload，如果為 nil 則使用儲存的 payload
		targetPayload := payload
		if targetPayload == nil {
			targetPayload = s.payload
			s.logger().Debug(ctx, "Using stored payload for token management")
		}

		// 創建新的對話會話，傳入總結和 payload
		err = s.createNewChat(ctx, targetPayload, summary)
		if err != nil {
			s.logger().Error(ctx, "Failed to create new chat with summary:", err)
			return err
		}

		s.logger().Info(ctx, "Successfully created new conversation with summary and reloaded payload")
		return nil
	}

	s.logger().Debugf(ctx, "Token count (%d) is within threshold (%d)",
		tokenCount, consts.AoAiTokenThreshold)
	return nil
}

// generateConversationSummary 生成對話總結但不重置歷史記錄
// 返回總結文本，用於創建新對話時的上下文
func (s *AoAi) generateConversationSummary(ctx context.Context) (string, error) {
	s.logger().Debug(ctx, "Generating conversation summary")

	// 第一步：讀取歷史記錄（garray 自動處理線程安全）
	historySlice := s.history.Slice()
	historyLen := len(historySlice)

	if historyLen < 3 {
		// 對話太短，不需要總結
		s.logger().Debug(ctx, "Conversation too short, no summarization needed")
		return "", nil
	}

	// 創建臨時歷史記錄用於總結（不修改原始歷史）
	tempHistory := make([]langchainllms.MessageContent, historyLen)
	for i, item := range historySlice {
		tempHistory[i] = item.(langchainllms.MessageContent)
	}

	// 第二步：在沒有鎖的情況下進行 API 調用
	// 添加總結提示
	summaryPrompt := "Please summarize our conversation so far in a concise manner, focusing on the key points and context."
	tempHistory = append(tempHistory, langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeHuman,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart(summaryPrompt),
		},
	})

	s.logger().Debug(ctx, "Calling API to generate conversation summary (read-only)...")

	// 獲取總結
	resp, err := s.llm.GenerateContent(
		ctx,
		tempHistory,
		langchainllms.WithTemperature(consts.AoAiSummaryTemp), // 使用較低的溫度以獲得更確定性的總結
		langchainllms.WithMaxTokens(s.maxOutputTokens/4),      // 限制總結的長度
	)

	if err != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, err, "Failed to generate conversation summary")
		s.logger().Error(ctx, wrappedErr)
		return "", wrappedErr
	}

	// 提取總結文本
	var summary string
	if len(resp.Choices) > 0 && len(resp.Choices[0].Content) > 0 {
		summary = resp.Choices[0].Content
		s.logger().Debugf(ctx, "Generated summary: %s", summary)
	} else {
		err = gerror.New("Failed to generate summary: empty response from API")
		s.logger().Error(ctx, err)
		return "", err
	}

	return summary, nil
}

// Chat 處理聊天消息並返回響應
// 包含完整的參數驗證、token 管理和錯誤處理
func (s *AoAi) Chat(ctx context.Context, message *llm.Message) (response *llm.ResponseData, err error) {
	// 參數驗證
	if message == nil {
		err = gerror.New("message parameter cannot be nil")
		s.logger().Error(ctx, err)
		return nil, err
	}

	if g.IsEmpty(message.Content) {
		err = gerror.New("message content cannot be empty")
		s.logger().Error(ctx, err)
		return nil, err
	}

	s.logger().Infof(ctx, "Received chat message: %v", gjson.New(message).MustToJsonIndentString())

	// 初始化響應對象
	response = &llm.ResponseData{}

	// 檢查並管理 token 數量，使用儲存的 payload 進行重新載入
	err = s.checkAndManageTokensWithPayload(ctx, s.payload)
	if err != nil {
		s.logger().Error(ctx, "Failed to manage tokens:", err)
		return nil, err
	}

	// 根據內容類型處理消息
	switch message.ContentType {
	case consts.ContentTypeText:
		// 處理文本消息，支持工具調用
		aiMessage, err := s.getResponseWithTools(ctx, gconv.String(message.Content))
		if err != nil {
			s.logger().Error(ctx, "Failed to get AI response:", err)
			return nil, err
		}
		response.Response = aiMessage

	case consts.ContentMediaFile:
		// 目前 Azure OpenAI 不直接支持媒體文件處理
		s.logger().Noticef(ctx, "Azure OpenAI does not directly support media files: %v", message.MimeType)
		err = gerror.New("Unsupported content type: media file")
		return nil, err

	default:
		err = gerror.Newf("Unsupported content type: %v", message.ContentType)
		s.logger().Error(ctx, err)
		return nil, err
	}

	// 驗證響應內容
	if g.IsEmpty(response.Response) {
		err = gerror.New("Failed to generate response: empty response content")
		s.logger().Error(ctx, err)
		return nil, err
	}

	// 計算並設置 token 數量（可選）
	if tokenCount, tokenErr := s.calculateTokenCount(ctx); tokenErr == nil {
		response.TotalTokenCount = int32(tokenCount)
	}

	s.logger().Debugf(ctx, "Successfully generated response: %s", response.Response)
	return response, nil
}

// GenerateContent 統一的內容生成接口，支援智能續寫和完整響應處理
// 實現跨模型一致的內容生成功能
func (s *AoAi) GenerateContent(ctx context.Context, request *llm.GenerateContentRequest) (*llm.GenerateContentResponse, error) {
	startTime := gtime.TimestampMilli()

	// 參數驗證
	if request == nil {
		err := gerror.New("request cannot be nil")
		s.logger().Error(ctx, err)
		return nil, err
	}

	if g.IsEmpty(request.Prompt) {
		err := gerror.New("prompt cannot be empty")
		s.logger().Error(ctx, err)
		return nil, err
	}

	// 檢查 LLM 實例是否已初始化
	if s.llm == nil {
		err := gerror.New("aoai llm instance is not initialized")
		s.logger().Error(ctx, err)
		return nil, err
	}

	// 應用預設值
	s.applyDefaults(ctx, request)

	// 記錄開始生成的日誌
	s.logger().Infof(ctx, "Starting AoAi Generate Content: prompt_length=%d, max_continuations=%d, token_budget=%d",
		len(request.Prompt), request.MaxContinuations, request.TotalTokenBudget)

	// 初始化響應結構
	response := &llm.GenerateContentResponse{
		LLMName:           s.modelName,
		InputContent:      request.Prompt,
		ContinuationCount: 0,
		IsComplete:        false,
		SafetyWarnings:    make([]string, 0),
	}

	// 執行智能續寫生成
	err := s.executeGenerationWithContinuation(ctx, request, response)
	if err != nil {
		return nil, err
	}

	// 計算生成時間
	response.GenerationTime = gtime.TimestampMilli() - startTime

	// 記錄完成日誌
	s.logger().Infof(ctx, "AoAi Generate Content completed: model=%s, continuations=%d/%d, input_tokens=%d, output_tokens=%d, total_tokens=%d, complete=%v, duration=%dms",
		response.LLMName, response.ContinuationCount, request.MaxContinuations,
		response.InputTokens, response.OutputTokens, response.TotalTokens,
		response.IsComplete, response.GenerationTime)

	return response, nil
}

// applyDefaults 應用預設配置值
func (s *AoAi) applyDefaults(ctx context.Context, request *llm.GenerateContentRequest) {
	if request.MaxContinuations <= 0 {
		defaultValue, _ := g.Cfg().Get(ctx, consts.ConfigLLMsAoAiMaxContinuations, consts.DefaultMaxContinuations)
		request.MaxContinuations = gconv.Int(defaultValue)
	}

	if request.TotalTokenBudget <= 0 {
		defaultValue, _ := g.Cfg().Get(ctx, consts.ConfigLLMsAoAiTotalTokenBudget, int32(s.maxOutputTokens*2))
		request.TotalTokenBudget = gconv.Int32(defaultValue)
	}

	if request.Temperature == nil {
		request.Temperature = &s.temperature
	}
}

// executeGenerationWithContinuation 執行智能續寫生成
func (s *AoAi) executeGenerationWithContinuation(ctx context.Context, request *llm.GenerateContentRequest, response *llm.GenerateContentResponse) error {
	var allContent strings.Builder
	var totalInputTokens, totalOutputTokens int32
	continuationCount := 0

	for continuationCount <= request.MaxContinuations {
		// 構建當前請求的 prompt
		currentPrompt := request.Prompt
		if continuationCount > 0 {
			currentPrompt = allContent.String() + "\n\n[請繼續完成上述內容]"
		}

		// 檢查 token 預算
		if totalInputTokens+totalOutputTokens >= request.TotalTokenBudget {
			s.logger().Debugf(ctx, "Token budget exceeded: %d/%d", totalInputTokens+totalOutputTokens, request.TotalTokenBudget)
			break
		}

		// 調用 AoAi API
		apiResponse, tokens, err := s.callAoAiAPI(ctx, currentPrompt, request)
		if err != nil {
			return err
		}

		// 累加內容和 token
		allContent.WriteString(apiResponse)
		totalInputTokens += tokens.InputTokens
		totalOutputTokens += tokens.OutputTokens

		// 檢測是否需要續寫
		isComplete, reason := s.isContentComplete(allContent.String())

		s.logger().Debugf(ctx, "AoAi Continuation %d: reason=%s, tokens_used=%d/%d, complete=%v",
			continuationCount, reason, totalInputTokens+totalOutputTokens, request.TotalTokenBudget, isComplete)

		if isComplete {
			response.IsComplete = true
			break
		}

		continuationCount++
	}

	// 設置最終響應數據
	response.OutputContent = allContent.String()
	response.InputTokens = totalInputTokens
	response.OutputTokens = totalOutputTokens
	response.TotalTokens = totalInputTokens + totalOutputTokens
	response.ContinuationCount = continuationCount

	return nil
}

// isContentComplete 檢測內容是否完整
func (s *AoAi) isContentComplete(content string) (bool, string) {
	// 檢測未完成的程式碼區塊
	if gstr.Count(content, "```")%2 != 0 {
		return false, "incomplete_code_block"
	}

	// 檢測句子是否在合適位置結束
	trimmed := gstr.Trim(content)
	if g.IsEmpty(trimmed) {
		return false, "empty_content"
	}

	// 檢測常見的未完成標記
	incompletePatterns := []string{"...", "待續", "to be continued", "（未完）", "未完待續", "continue", "more"}
	for _, pattern := range incompletePatterns {
		if gstr.ContainsI(content, pattern) {
			return false, "explicit_incomplete_marker"
		}
	}

	// 檢測是否以不完整的句子結尾
	lastChar := gstr.SubStrRune(trimmed, -1, 1)
	if !gstr.InArray([]string{".", "。", "!", "！", "?", "？", "}", "]", ")", "\"", "'", "`"}, lastChar) {
		// 如果內容較短，可能是正常結束
		if len(trimmed) < 50 {
			return true, "short_content_complete"
		}
		return false, "incomplete_sentence"
	}

	return true, "complete"
}

// TokenInfo AoAi Token 統計信息
type TokenInfo struct {
	InputTokens  int32
	OutputTokens int32
}

// callAoAiAPI 調用 AoAi API
func (s *AoAi) callAoAiAPI(ctx context.Context, prompt string, request *llm.GenerateContentRequest) (string, *TokenInfo, error) {
	// 構建消息歷史（不修改實例的歷史記錄）
	messages := make([]langchainllms.MessageContent, consts.DBResultEmpty)

	// 添加系統指令（如果有）
	if !g.IsEmpty(request.SystemInstruction) {
		messages = append(messages, langchainllms.MessageContent{
			Role: langchainllms.ChatMessageTypeSystem,
			Parts: []langchainllms.ContentPart{
				langchainllms.TextPart(request.SystemInstruction),
			},
		})
	}

	// 添加用戶消息
	messages = append(messages, langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeHuman,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart(prompt),
		},
	})

	// 設置生成選項
	temperature := s.temperature
	if request.Temperature != nil {
		temperature = *request.Temperature
	}

	var resp *langchainllms.ContentResponse
	var lastErr error

	// 實現重試機制，與現有 getResponse 方法保持一致
	for attempt := consts.CacheExistsTrue; attempt <= consts.AoAiMaxRetryAttempts; attempt++ {
		resp, lastErr = s.llm.GenerateContent(
			ctx,
			messages,
			langchainllms.WithTemperature(gconv.Float64(temperature)),
			langchainllms.WithMaxTokens(s.maxOutputTokens),
		)

		if lastErr == nil {
			s.logger().Debugf(ctx, "AoAi API call succeeded on attempt %d", attempt)
			break
		}

		// 記錄錯誤並準備重試
		s.logger().Infof(ctx, "AoAi API call failed, attempt %d/%d: %v",
			attempt, consts.AoAiMaxRetryAttempts, lastErr)

		// 如果不是最後一次嘗試，則等待後重試
		if attempt < consts.AoAiMaxRetryAttempts {
			vTtl, _ := g.Cfg().Get(ctx, consts.ConfigSystemAISendRetryTTL, consts.TimeFormatDuration40Sec)
			time.Sleep(vTtl.Duration())
		}
	}

	if lastErr != nil {
		return "", nil, gerror.WrapCode(consts.ApiFailed, lastErr, "failed to call AoAi API after retries")
	}

	// 驗證響應
	if resp == nil || len(resp.Choices) == 0 || len(resp.Choices[0].Content) == 0 {
		return "", nil, gerror.New("empty response from AoAi API")
	}

	// 提取響應內容
	aiResponse := resp.Choices[0].Content

	// 計算 Token 統計（簡化實現，實際可能需要更精確的計算）
	tokenInfo := &TokenInfo{
		InputTokens:  s.estimateTokens(prompt),
		OutputTokens: s.estimateTokens(aiResponse),
	}

	return aiResponse, tokenInfo, nil
}

// estimateTokens 估算文本的 Token 數量
// 這是一個簡化的實現，實際應用中可能需要使用更精確的 tokenizer
func (s *AoAi) estimateTokens(text string) int32 {
	if g.IsEmpty(text) {
		return consts.DBResultEmpty
	}

	// 簡化估算：平均每個 token 約 4 個字符（英文）或 1.5 個字符（中文）
	// 這裡使用混合估算
	textLength := len(text)
	chineseCount := 0

	// 簡單統計中文字符數量
	for _, r := range text {
		if r >= 0x4e00 && r <= 0x9fff {
			chineseCount++
		}
	}

	// 估算 token 數量
	englishChars := textLength - chineseCount
	estimatedTokens := int32(chineseCount/2 + englishChars/4)

	// 添加一些開銷
	estimatedTokens += consts.AoAiTokenOverhead

	return estimatedTokens
}

// GenerateContentSimple 簡化版本，向後兼容
func (s *AoAi) GenerateContentSimple(ctx context.Context, prompt string) (string, error) {
	request := &llm.GenerateContentRequest{
		Prompt:           prompt,
		MaxContinuations: 3,
		TotalTokenBudget: int32(s.maxOutputTokens * 2),
		IncludeThinking:  false,
	}

	response, err := s.GenerateContent(ctx, request)
	if err != nil {
		return "", err
	}

	return response.OutputContent, nil
}

// Release 釋放 Azure OpenAI 服務資源
// 清理對話歷史記錄和相關資源
func (s *AoAi) Release(ctx context.Context) {
	s.logger().Debug(ctx, "Releasing Azure OpenAI service resources")

	// 清理對話歷史記錄（garray 自動處理線程安全）
	if s.history != nil {
		s.history.Clear()
		s.history = nil
	}

	// 清理 Token 緩存
	if s.tokenCache != nil {
		s.tokenCache.mutex.Lock()
		s.tokenCache.count = 0
		s.tokenCache.timestamp = time.Time{}
		s.tokenCache.mutex.Unlock()
		s.tokenCache = nil
	}

	// 清理 payload 引用
	s.payload = nil

	// 清理 LLM 實例引用
	// 注意：openai.LLM 通常不需要顯式關閉，但我們清理引用以幫助 GC
	s.llm = nil

	// 重置其他欄位
	s.temperature = 0
	s.maxOutputTokens = 0
	s.modelName = ""

	s.logger().Info(ctx, "Azure OpenAI service resources released successfully")
}

// createHistoryCopy 創建歷史記錄的副本
func (s *AoAi) createHistoryCopy() []langchainllms.MessageContent {
	historySlice := s.history.Slice()
	historyCopy := make([]langchainllms.MessageContent, len(historySlice))
	for i, item := range historySlice {
		historyCopy[i] = item.(langchainllms.MessageContent)
	}
	return historyCopy
}

// convertToOpenAITools 轉換工具定義為 OpenAI 格式
func (s *AoAi) convertToOpenAITools(toolDefinitions []common.ToolDefinition) []langchainllms.Tool {
	var tools []langchainllms.Tool

	for _, toolDef := range toolDefinitions {
		tool := langchainllms.Tool{
			Type: "function",
			Function: &langchainllms.FunctionDefinition{
				Name:        toolDef.Name,
				Description: toolDef.Description,
				Parameters:  toolDef.Parameters,
			},
		}
		tools = append(tools, tool)
	}

	return tools
}

// callLLMWithRetry 帶重試機制的 LLM 調用
func (s *AoAi) callLLMWithRetry(ctx context.Context, historyCopy []langchainllms.MessageContent, options []langchainllms.CallOption) (*langchainllms.ContentResponse, error) {
	s.logger().Infof(ctx, "=== START call LLM With Retry ===")
	s.logger().Debugf(ctx, "Retry configuration: max_attempts=%d", consts.AoAiMaxRetryAttempts)
	s.logger().Debugf(ctx, "History messages count: %d", len(historyCopy))
	s.logger().Debugf(ctx, "Call options count: %d", len(options))

	var resp *langchainllms.ContentResponse
	var lastErr error

	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		s.logger().Infof(ctx, "=== END call LLM With Retry === Duration: %v", duration)
	}()

	for attempt := 1; attempt <= consts.AoAiMaxRetryAttempts; attempt++ {
		attemptStartTime := time.Now()
		s.logger().Infof(ctx, "Starting API call attempt %d/%d...", attempt, consts.AoAiMaxRetryAttempts)

		// 檢查 context 是否已被取消
		if ctx.Err() != nil {
			s.logger().Error(ctx, "Context cancelled before API call:", ctx.Err())
			return nil, ctx.Err()
		}

		resp, lastErr = s.llm.GenerateContent(ctx, historyCopy, options...)
		attemptDuration := time.Since(attemptStartTime)

		if lastErr == nil {
			s.logger().Infof(ctx, "API call succeeded on attempt %d, duration: %v", attempt, attemptDuration)
			s.logger().Debugf(ctx, "Response received with %d choices", len(resp.Choices))
			break
		}

		s.logger().Errorf(ctx, "API call failed on attempt %d/%d (duration: %v): %v",
			attempt, consts.AoAiMaxRetryAttempts, attemptDuration, lastErr)

		if attempt < consts.AoAiMaxRetryAttempts {
			vTtl, _ := g.Cfg().Get(ctx, consts.ConfigSystemAISendRetryTTL, consts.TimeFormatDuration40Sec)
			retryDelay := vTtl.Duration()
			s.logger().Infof(ctx, "Waiting %v before retry attempt %d...", retryDelay, attempt+1)
			time.Sleep(retryDelay)
		}
	}

	if lastErr != nil {
		wrappedErr := gerror.WrapCode(consts.ApiFailed, lastErr,
			fmt.Sprintf("Failed to generate response after %d attempts", consts.AoAiMaxRetryAttempts))
		s.logger().Error(ctx, "All retry attempts exhausted:", wrappedErr)
		return nil, wrappedErr
	}

	s.logger().Infof(ctx, "LLM call completed successfully")
	return resp, nil
}

// processResponse 處理包含工具調用的響應
func (s *AoAi) processResponse(ctx context.Context, resp *langchainllms.ContentResponse, toolDefinitions []common.ToolDefinition) (string, error) {
	s.logger().Infof(ctx, "=== START process Response ===")
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		s.logger().Infof(ctx, "=== END process Response === Duration: %v", duration)
	}()

	// 驗證響應結構
	s.logger().Debugf(ctx, "Validating LLM response structure...")
	if resp == nil {
		err := gerror.New("received nil response from LLM")
		s.logger().Error(ctx, err)
		return "", err
	}

	if len(resp.Choices) == 0 {
		err := gerror.New("empty response choices from LLM")
		s.logger().Error(ctx, err)
		return "", err
	}

	s.logger().Debugf(ctx, "Response validation passed: %d choices available", len(resp.Choices))
	choice := resp.Choices[0]
	s.logger().Debugf(ctx, "Processing first choice with %d tool calls", len(choice.ToolCalls))

	// 檢查是否有工具調用請求
	if len(choice.ToolCalls) > 0 {
		s.logger().Infof(ctx, "LLM requested %d tool calls, entering tool execution flow", len(choice.ToolCalls))

		// 創建助手消息並添加到歷史
		s.logger().Debugf(ctx, "Adding assistant message to history...")
		assistantMessage := langchainllms.MessageContent{
			Role:  langchainllms.ChatMessageTypeAI,
			Parts: []langchainllms.ContentPart{langchainllms.TextPart("")},
		}
		s.history.Append(assistantMessage)
		s.logger().Debugf(ctx, "Assistant message added to history")

		// 執行工具調用
		s.logger().Infof(ctx, "Starting tool execution phase...")
		for i, toolCall := range choice.ToolCalls {
			s.logger().Infof(ctx, "Executing tool call %d/%d: %s", i+1, len(choice.ToolCalls), toolCall.FunctionCall.Name)

			toolStartTime := time.Now()
			result, err := s.executeToolCall(ctx, toolCall)
			toolDuration := time.Since(toolStartTime)

			if err != nil {
				s.logger().Errorf(ctx, "Tool call %d failed (duration: %v): %v", i+1, toolDuration, err)
			} else {
				s.logger().Infof(ctx, "Tool call %d completed successfully (duration: %v)", i+1, toolDuration)
			}

			// 添加工具結果到歷史
			s.logger().Debugf(ctx, "Adding tool result to history for call ID: %s", toolCall.ID)
			s.addToolResultToHistory(toolCall.ID, result)
		}
		s.logger().Infof(ctx, "All tool calls completed, generating final response...")

		// 讓 LLM 基於工具結果生成最終回應
		return s.generateFinalResponse(ctx, toolDefinitions)
	}

	// 沒有工具調用，直接返回回應
	s.logger().Debugf(ctx, "No tool calls detected, processing direct response...")
	aiResponse := choice.Content
	if aiResponse == "" {
		err := gerror.New("empty message content in LLM response")
		s.logger().Error(ctx, err)
		return "", err
	}

	s.logger().Debugf(ctx, "Direct response content length: %d characters", len(aiResponse))

	// 添加到歷史
	s.logger().Debugf(ctx, "Adding AI response to history...")
	s.history.Append(langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeAI,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart(aiResponse),
		},
	})
	s.logger().Debugf(ctx, "AI response added to history successfully")

	// 使 Token 緩存失效
	s.logger().Debugf(ctx, "Invalidating token cache...")
	s.invalidateTokenCache(ctx)
	s.logger().Debugf(ctx, "Token cache invalidated")

	s.logger().Infof(ctx, "Direct response processing completed successfully")
	return aiResponse, nil
}

// executeToolCall 執行單個工具調用
func (s *AoAi) executeToolCall(ctx context.Context, toolCall langchainllms.ToolCall) (*common.ToolResult, error) {
	s.logger().Infof(ctx, "=== START execute Tool Call ===")
	s.logger().Infof(ctx, "Executing tool: %s with args: %s",
		toolCall.FunctionCall.Name, toolCall.FunctionCall.Arguments)

	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		s.logger().Infof(ctx, "=== END execute Tool Call === Duration: %v", duration)
	}()

	// 工具調用功能已移除（MCP 已移除）
	s.logger().Warningf(ctx, "Tool call attempted but MCP integration has been removed: %s", toolCall.FunctionCall.Name)
	return &common.ToolResult{
		Success: false,
		Error:   "Tool execution not available (MCP integration removed)",
	}, gerror.New("MCP integration has been removed from the system")

}

// addToolResultToHistory 添加工具結果到歷史
func (s *AoAi) addToolResultToHistory(toolCallID string, result *common.ToolResult) {
	content := result.Content
	if !result.Success {
		content = fmt.Sprintf("Tool execution failed: %s", result.Error)
	}

	// 創建工具結果消息
	toolMessage := langchainllms.MessageContent{
		Role: langchainllms.ChatMessageTypeTool,
		Parts: []langchainllms.ContentPart{
			langchainllms.TextPart(content),
		},
	}

	s.history.Append(toolMessage)
}

// generateFinalResponse 生成基於工具結果的最終回應
func (s *AoAi) generateFinalResponse(ctx context.Context, toolDefinitions []common.ToolDefinition) (string, error) {
	s.logger().Infof(ctx, "=== START generate Final Response ===")
	s.logger().Debugf(ctx, "Generating final response with %d tool definitions", len(toolDefinitions))

	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		s.logger().Infof(ctx, "=== END generate Final Response === Duration: %v", duration)
	}()

	// 準備歷史記錄副本
	s.logger().Debugf(ctx, "Creating history copy for final response generation...")
	historyCopy := s.createHistoryCopy()
	s.logger().Debugf(ctx, "History copy created with %d messages", len(historyCopy))

	// 準備調用選項
	s.logger().Debugf(ctx, "Preparing call options for final response...")
	options := []langchainllms.CallOption{
		langchainllms.WithTemperature(gconv.Float64(s.temperature)),
		langchainllms.WithMaxTokens(s.maxOutputTokens),
	}
	s.logger().Debugf(ctx, "Base options set: temperature=%.2f, maxTokens=%d", s.temperature, s.maxOutputTokens)

	// 保持工具定義，支持多輪工具調用
	if len(toolDefinitions) > 0 {
		s.logger().Debugf(ctx, "Adding %d tool definitions for potential multi-turn tool calls", len(toolDefinitions))
		tools := s.convertToOpenAITools(toolDefinitions)
		options = append(options, langchainllms.WithTools(tools))
		s.logger().Debugf(ctx, "Tools added to options for multi-turn support")
	} else {
		s.logger().Debugf(ctx, "No tool definitions to add for final response")
	}

	// 調用 LLM 生成最終回應
	s.logger().Infof(ctx, "Calling LLM for final response generation...")
	resp, err := s.callLLMWithRetry(ctx, historyCopy, options)
	if err != nil {
		s.logger().Error(ctx, "Failed to generate final response:", err)
		return "", err
	}
	s.logger().Infof(ctx, "Final response LLM call completed successfully")

	// 遞歸處理（支持多輪工具調用）
	s.logger().Infof(ctx, "Processing final response (supports recursive tool calls)...")
	result, err := s.processResponse(ctx, resp, toolDefinitions)
	if err != nil {
		s.logger().Error(ctx, "Failed to process final response:", err)
		return "", err
	}
	s.logger().Infof(ctx, "Final response processing completed successfully")

	return result, nil
}

// New 創建新的 Azure OpenAI 服務實例
// 返回實現 ILLMs 接口的實例
func New() llms.ILLMs {
	return &AoAi{
		history: garray.New(true), // 初始化線程安全的 garray
	}
}
