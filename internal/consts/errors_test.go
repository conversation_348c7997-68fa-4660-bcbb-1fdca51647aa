package consts

import (
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// TestErrorConstants 測試錯誤常量定義
func TestErrorConstants(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 驗證基本錯誤常量
		t.<PERSON><PERSON><PERSON>(Success.Code(), 0)
		t.<PERSON><PERSON><PERSON>(Success.Message(), "success")
		
		t.<PERSON><PERSON><PERSON>(Failed.Code(), -1)
		t.<PERSON><PERSON><PERSON>(Failed.Message(), "failed")
		
		t.<PERSON><PERSON><PERSON>(InvalidateService.Code(), -2)
		t.<PERSON><PERSON><PERSON>(InvalidateService.Message(), "invalid service")
		
		// 驗證新增的業務錯誤常量
		t.<PERSON>ser<PERSON>(ErrTableNotExists.Code(), 1001)
		t.<PERSON>ser<PERSON>(ErrTableNotExists.Message(), "table does not exist")
		
		t.<PERSON>ser<PERSON>(ErrDatabaseError.Code(), 1002)
		t.<PERSON><PERSON><PERSON>(ErrDatabaseError.Message(), "database error")
		
		t.<PERSON><PERSON><PERSON>(ErrInvalidInput.Code(), 1003)
		t.<PERSON><PERSON><PERSON>(ErrInvalidInput.Message(), "invalid input")
		
		t.<PERSON><PERSON><PERSON>(ErrUnauthorized.Code(), 1004)
		t.Assert(ErrUnauthorized.Message(), "unauthorized")
	})
}
