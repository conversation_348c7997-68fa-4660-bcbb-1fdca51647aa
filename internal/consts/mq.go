package consts

import "github.com/gogf/gf/v2/frame/g"

const (
	ExchangeName = "dsh"

	RouteKeyMariadbPrefix  = "mariadb."
	RouteKeyWeaviatePrefix = "weaviate."

	RouteKeyAMS        = "ams"
	RouteKeyTenant     = "tenant"
	RouteKeyQuizto     = "quizto"
	RouteKeyChannelHub = "channelHub"
	RouteKeyBrainHub   = "brainHub"
)

var RouteKeys = []string{
	RouteKeyMariadbPrefix + RouteKeyAMS,
	RouteKeyMariadbPrefix + RouteKeyTenant,
	RouteKeyMariadbPrefix + RouteKeyQuizto,
	RouteKeyMariadbPrefix + RouteKeyChannelHub,
	RouteKeyMariadbPrefix + RouteKeyBrainHub,

	RouteKeyWeaviatePrefix + RouteKeyAMS,
	RouteKeyWeaviatePrefix + RouteKeyTenant,
	RouteKeyWeaviatePrefix + RouteKeyQuizto,
	RouteKeyWeaviatePrefix + RouteKeyBrainHub,
}

// the action for weaviate
const (
	ActionCreateCollection  = "create_collection"
	ActionAddNewProperties  = "add_new_properties"
	ActionCreateData        = "create_data"
	ActionUpdateProperties  = "update_properties"
	ActionCreateTenant      = "create_tenant"
	ActionClearDataByFilter = "clear_data_by_filter"
	ActionEmptyCollection   = "empty_collection"
	ActionDeleteCollection  = "delete_collection"
	ActionDeleteTenants     = "delete_tenants"
	ActionUpdateVector      = "update_vector"
)

// the type for mariadb  action
const (
	ActionInsert         = "insert"
	ActionDelete         = "delete"
	ActionUpdate         = "update"
	ActionUpdateOrInsert = "updateOrInsert"
	ActionCreateSchema   = "createSchema"
	ActionCreateTable    = "createTable"
)

const (
	ResourceTypeFile        = "file"
	ResourceTypeURL         = "url"
	ResourceTypePlainText   = "plain_text"
	ResourceTypeYoutubeLink = "youtube_link"
)

var MapResourceTypeToField = g.MapStrStr{
	ResourceTypeFile:        "upload_files",
	ResourceTypeURL:         "url_contents",
	ResourceTypePlainText:   "plain_text_contents",
	ResourceTypeYoutubeLink: "youtube_contents",
}
