package queryapi

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/queryapi/v1"
)

func (c *ControllerV1) DataQuery(ctx context.Context, req *v1.DataQueryReq) (res *v1.DataQueryRes, err error) {
	res = &v1.DataQueryRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	var out *model.GetContentsRes
	cost := gtime.FuncCost(func() {
		out, err = service.MariaDB().GetContents(ctx, req.GetContentsReq)

	})
	if err != nil {
		// 檢查是否為表不存在的錯誤
		if gerror.Code(err).Code() == consts.ErrTableNotExists.Code() {
			//if codeErr, ok := err.(gcode.Code); ok && codeErr.Code() == consts.ErrTableNotExists.Code() {
			res.Code = consts.ErrTableNotExists.Code()
			res.Message = consts.ErrTableNotExists.Message()
		} else {
			res.Code = consts.Failed.Code()
			res.Message = err.Error()
		}
	}
	res.Cost = cost.String()

	if out != nil {
		res.Contents = out.Contents
	} else {
		res.Contents = make([]map[string]any, 0)
	}

	g.RequestFromCtx(ctx).Response.WriteJson(res)

	return

}
