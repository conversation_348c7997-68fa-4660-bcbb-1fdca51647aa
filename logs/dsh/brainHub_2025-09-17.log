2025-09-17T21:11:10.109+08:00 [DEBU] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/dsh.(*sDSH).sendRequest] dsh.go:202: send to url=http://dsh.svc/v1/createCollection ,request={
	"collection_name": "Chat_message",
	"multi_tenancy": true,
	"properties": [
		{
			"dataType": [
				"text"
			],
			"description": "",
			"indexFilterable": null,
			"indexInverted": null,
			"indexSearchable": null,
			"moduleConfig": {
				"text2vec-openai": {
					"skip": true,
					"vectorizePropertyName": false
				}
			},
			"name": "message",
			"nestedProperties": null,
			"tokenization": "gse"
		},
		{
			"dataType": [
				"text"
			],
			"description": "",
			"indexFilterable": null,
			"indexInverted": null,
			"indexSearchable": null,
			"moduleConfig": {
				"text2vec-openai": {
					"skip": true,
					"vectorizePropertyName": false
				}
			},
			"name": "service_id",
			"nestedProperties": null,
			"tokenization": ""
		},
		{
			"dataType": [
				"text"
			],
			"description": "",
			"indexFilterable": null,
			"indexInverted": null,
			"indexSearchable": null,
			"moduleConfig": {
				"text2vec-openai": {
					"skip": true,
					"vectorizePropertyName": false
				}
			},
			"name": "user_id",
			"nestedProperties": null,
			"tokenization": ""
		},
		{
			"dataType": [
				"text"
			],
			"description": "",
			"indexFilterable": null,
			"indexInverted": null,
			"indexSearchable": null,
			"moduleConfig": {
				"text2vec-openai": {
					"skip": true,
					"vectorizePropertyName": false
				}
			},
			"name": "channel",
			"nestedProperties": null,
			"tokenization": ""
		},
		{
			"dataType": [
				"text"
			],
			"description": "",
			"indexFilterable": null,
			"indexInverted": null,
			"indexSearchable": null,
			"moduleConfig": {
				"text2vec-openai": {
					"skip": true,
					"vectorizePropertyName": false
				}
			},
			"name": "message_type",
			"nestedProperties": null,
			"tokenization": ""
		},
		{
			"dataType": [
				"text"
			],
			"description": "",
			"indexFilterable": null,
			"indexInverted": null,
			"indexSearchable": null,
			"moduleConfig": {
				"text2vec-openai": {
					"skip": true,
					"vectorizePropertyName": false
				}
			},
			"name": "display_name",
			"nestedProperties": null,
			"tokenization": ""
		},
		{
			"dataType": [
				"date"
			],
			"description": "",
			"indexFilterable": null,
			"indexInverted": null,
			"indexSearchable": null,
			"moduleConfig": {
				"text2vec-openai": {
					"skip": true,
					"vectorizePropertyName": false
				}
			},
			"name": "crate_at",
			"nestedProperties": null,
			"tokenization": ""
		}
	],
	"renew_settings": true,
	"tenants": [],
	"vector_properties": [
		"message"
	]
}
2025-09-17T21:11:20.130+08:00 [ERRO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/dsh.(*sDSH).sendRequest] dsh.go:205: request failed: Post "http://************:8087/v1/createCollection": EOF 
Stack:
1.  brainHub/internal/logic/dsh.(*sDSH).sendRequest
    /Users/<USER>/Source/Ai app/brainHub/internal/logic/dsh/dsh.go:205
2.  brainHub/internal/logic/dsh.(*sDSH).SendRequest
    /Users/<USER>/Source/Ai app/brainHub/internal/logic/dsh/dsh.go:449
3.  brainHub/internal/cmd.createChatMessageCollection
    /Users/<USER>/Source/Ai app/brainHub/internal/cmd/cmd.go:109
4.  brainHub/internal/cmd.initVectorCollections
    /Users/<USER>/Source/Ai app/brainHub/internal/cmd/cmd.go:84
5.  brainHub/internal/cmd.init.func1
    /Users/<USER>/Source/Ai app/brainHub/internal/cmd/cmd.go:38
6.  main.main
    /Users/<USER>/Source/Ai app/brainHub/main.go:18

