2025-09-17T21:11:10.107+08:00 [INFO] Successfully connected to RabbitMQ
2025-09-17T21:11:10.108+08:00 [INFO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/messageQ.InitMessageHandlers] init.go:13: Initializing message handlers
2025-09-17T21:11:10.108+08:00 [INFO] [brainHub/internal/logic/messageQ.(*sMessageQ).RegisterHandler] messageQ.go:179: Registered message handler for route key prefix: payloadChanged
2025-09-17T21:11:10.108+08:00 [INFO] [brainHub/internal/logic/messageQ.RegisterPayloadChangedHandler] payload_handler.go:121: Registered payloadChanged message handler
2025-09-17T21:11:10.108+08:00 [INFO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/messageQ.InitMessageHandlers] init.go:23: Message handlers initialized successfully
2025-09-17T21:11:10.108+08:00 [INFO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/messageQ.StartMessageConsumer] init.go:30: Starting message consumer
2025-09-17T21:11:10.108+08:00 [INFO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/messageQ.(*sMessageQ).InitReceive] messageQ.go:184: Initializing message receive service
2025-09-17T21:11:10.108+08:00 [INFO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/messageQ.(*sMessageQ).InitReceive] messageQ.go:205: Message receive service initialized successfully
2025-09-17T21:11:10.108+08:00 [INFO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/messageQ.StartMessageConsumer] init.go:39: Message consumer started successfully
2025-09-17T21:11:10.117+08:00 [DEBU] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/messageQ.(*sMessageQ).startConsuming] messageQ.go:352: Bound queue to routing key: payloadChanged
2025-09-17T21:11:10.119+08:00 [INFO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/logic/messageQ.(*sMessageQ).startConsuming] messageQ.go:369: Started consuming messages from queue: amq.gen-x9RiuNdEHXkHtEbnTwRwAQ
