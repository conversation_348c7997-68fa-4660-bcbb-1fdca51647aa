2025-09-17T21:11:10.076+08:00 [NOTI] ready to create MQ
2025-09-17T21:11:10.108+08:00 [INFO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/cmd.initVectorCollections] cmd.go:81: Initializing vector collections
2025-09-17T21:11:10.108+08:00 [INFO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/cmd.createChatMessageCollection] cmd.go:95: Creating chat_message collection
2025-09-17T21:11:20.130+08:00 [ERRO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/cmd.createChatMessageCollection] cmd.go:111: Failed to create chat_message collection: request failed: Post "http://192.168.4.68:8087/v1/createCollection": EOF 
Stack:
1.  brainHub/internal/cmd.createChatMessageCollection
    /Users/<USER>/Source/Ai app/brainHub/internal/cmd/cmd.go:111
2.  brainHub/internal/cmd.initVectorCollections
    /Users/<USER>/Source/Ai app/brainHub/internal/cmd/cmd.go:84
3.  brainHub/internal/cmd.init.func1
    /Users/<USER>/Source/Ai app/brainHub/internal/cmd/cmd.go:38
4.  main.main
    /Users/<USER>/Source/Ai app/brainHub/main.go:18

2025-09-17T21:11:20.130+08:00 [ERRO] {d0963842fa1366181c79a93f6a1ad660} [brainHub/internal/cmd.init.func1] cmd.go:39: failed to create chat_message collection: request failed: Post "http://192.168.4.68:8087/v1/createCollection": EOF 
Stack:
1.  brainHub/internal/cmd.init.func1
    /Users/<USER>/Source/Ai app/brainHub/internal/cmd/cmd.go:39
2.  main.main
    /Users/<USER>/Source/Ai app/brainHub/main.go:18

