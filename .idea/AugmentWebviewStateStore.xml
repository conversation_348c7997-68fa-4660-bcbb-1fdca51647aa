<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;af03534d-b474-42f6-97a9-d3e9bb09cbcb&quot;,&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;conversations&quot;:{&quot;af03534d-b474-42f6-97a9-d3e9bb09cbcb&quot;:{&quot;id&quot;:&quot;af03534d-b474-42f6-97a9-d3e9bb09cbcb&quot;,&quot;createdAtIso&quot;:&quot;2025-09-17T12:00:01.643Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-09-17T12:11:19.390Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;adc171fd-0a6f-44d1-a626-9127abf6fe90&quot;,&quot;uuid&quot;:&quot;c6f960bd-680e-4aee-abb3-c07766b74159&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1758110401644,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;a7e3fba9-21b6-4586-a46d-98ee72305bb0&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:02:11.334Z&quot;,&quot;request_message&quot;:&quot;請使用 Context7 工具查詢並了解 https://github.com/modelcontextprotocol/go-sdk 的最新文檔和使用方法。然後基於該 SDK 撰寫一個完整的範例程式，作為 MCP (Model Context Protocol) 客戶端來連接 MCP 伺服器。\n\n具體要求：\n1. 使用 Context7 獲取 go-sdk 的最新資訊和 API 文檔\n2. 創建一個功能完整的 MCP 客戶端範例\n3. 客戶端需要支援以下連接參數配置：\n   - 連接類型：SSE (Server-Sent Events)\n   - 伺服器 URL：https://csai_uat_deepface.chainsea.com.tw/mcp/\n   - 請求標頭：CS-API-Key: \&quot;NxNNpIW3kL9yNfYZh7zyvSglfmoeB7AJyNZJsfUrbrU\&quot;\n   - 超時設定：60 秒\n   - 自動批准：空陣列 []\n   - 啟用狀態：enabled (disabled: false)\n\n4. 範例程式應該包含：\n   - 完整的連接設定\n   - 錯誤處理機制\n   - 基本的 MCP 協議通訊功能\n   - 適當的日誌輸出（英文）\n   - 清晰的中文註釋說明關鍵部分\n\n5. 請遵循 Go 最佳實踐和 goframe 框架規範\n6. 將相關常量定義在 internal/consts 目錄下\n7. 確保代碼功能原子化且獨立清晰&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;8883301b-5239-4ce8-a1e7-5487778d782c&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:02:23.851Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;bc97e7a8-2d81-4ba4-bfd5-fcb3bb068401&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:02:29.346Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;f65794b0-972d-4fd4-b325-7fb9e34bee1e&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:02:36.403Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;78bee849-1f4f-4a73-9ae1-1d75bf049629&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:02:43.144Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;f3d3a79b-436e-4af2-b9c8-abe1d4efea86&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:02:49.143Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;0243c517-be19-4eb1-bd4d-77deff648ffa&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:03:01.304Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;9b00e03c-d281-43fe-880b-1b5a55a80869&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:03:08.642Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;2fe2cffe-e3ed-415e-989a-12b8ca07e8ae&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:04:31.792Z&quot;,&quot;request_message&quot;:&quot; 可以使用goframe , 但是這只是一個測試，目錄結構先不用，代碼都寫在main.go中&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;b6a43e26-4fbf-476f-afe5-f5654eef20c0&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:05:21.501Z&quot;,&quot;request_message&quot;:&quot;可以開始 ，要做到mcp intialize 和tools/list 這兩個方法&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;a63750b1-f16e-4073-88c8-a9458703efb9&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:05:32.387Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;c36439a2-9055-45a6-95ad-4202ca745166&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:05:40.566Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;95ed8941-97ea-4f1b-b07d-567e9b37613b&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:05:51.021Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;c91b4fe3-5374-46fe-87af-63eea38bf194&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:05:56.305Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;8d54e341-988a-4c39-ba6c-a61e7529b51d&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:06:17.211Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;62a68ce3-5c61-444a-ace9-07f5cac34b9b&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:06:23.652Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;0ea9b32e-cb29-4e2d-aa4c-30169c1f353a&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:06:47.725Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;eb729fd2-95d3-478a-a5aa-1712708158c4&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:06:53.126Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;1dc8c02e-cb09-4ea5-9177-fc7e32ef718a&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:07:06.764Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;63706fba-79cb-4d09-8a08-01718b8d6377&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:07:12.973Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;60bc8a52-7a65-4af7-8db3-e755cdbda4d9&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:07:35.576Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;863b32f8-b977-406b-bc79-9718a616f865&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:07:41.695Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;a47b0998-0b15-4d94-8950-dfe9ca32bb38&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:08:00.764Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;c173182c-85af-4d5a-aa1a-8c178f425056&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:08:07.806Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;832e7a54-9f76-428d-88d1-33ab700a7cdd&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:08:17.922Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;48758644-7e23-4eb8-ab66-43434a49dad0&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:08:28.094Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;30175ab1-9d0b-44fd-b567-d6f6d7fdf2a8&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:08:59.681Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;4e85dad3-ba9a-45d7-9e34-88ea83bc76a1&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:09:08.509Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;f6dc2cd8-5d22-4579-819d-154454c41ea1&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:09:19.345Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;5bc2814d-1459-4e15-8f38-85dcabe35e86&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:09:26.940Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;598085d3-543b-48f9-862a-bbbd25a734e5&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:09:33.358Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;4cca2da9-7a09-4b01-89f4-04ca066c6533&quot;,&quot;timestamp&quot;:&quot;2025-09-17T12:11:19.390Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;request_id&quot;:&quot;c1910591-b23c-4bf8-b61f-e06516975b12&quot;,&quot;uuid&quot;:&quot;5efcc76c-9135-45c7-91ce-20c9c84ff48c&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:1758110401644,&quot;toTimestamp&quot;:1758111104127,&quot;seen_state&quot;:&quot;unseen&quot;}],&quot;feedbackStates&quot;:{&quot;temp-fe-7f8e663c-80ef-493d-9d0c-7b037a19f1b6&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-c507de81-ad74-402c-bfbd-cd104cf8fca5&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-524e62f4-432f-4c9e-91f2-335b1281087d&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-888136c4-6eb2-4e17-909e-91fd9b936cba&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-ea5ccdfa-dc03-44ce-ae0b-02febfac302f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-70e5ed47-0bd9-4e39-a7dd-9b52741cad5b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-6d9bc723-7344-40a9-b60e-b8e94eb54c3e&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-824734d5-0a0b-4771-ba51-0ec693c1a904&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-fbd1d679-0d3a-4734-8794-e83db70d3431&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-3e75b032-5ee3-42f7-a7cc-1115203ba348&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-a9e40443-fa8d-4b27-94ea-f4321141a64f&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-aa5dbcf4-4a31-4795-a419-98f5f86da6e0&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-3399d5c8-0568-4cfb-bc1c-f2810d15fefc&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-47871791-826b-4f67-a38a-b47555b45bad&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-9f1b7dfa-8eac-4951-841e-6899be638947&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-ccd361d6-65ad-4601-9077-8e592c95c0ab&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-7998cc67-1b98-43a5-8a31-c30cb4c9cda8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-81c168e0-fe8e-46a0-9d9f-b65e05662159&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-637ab194-8f50-441a-8a45-f2104e2cbb9d&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-62cbc51d-0146-464b-9741-e8e7dd64de55&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-5a55120b-f75e-4f65-b1d4-9841220daee0&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-f351cb6c-eece-4a5c-a2ec-04f055533f94&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-7b3b569d-8002-4996-8752-d5e28015b9fe&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-0e7c722a-6ff5-498f-b2f1-a69ec1d4653b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-c30edb7c-7281-4e51-8ef6-759c9b69ce9e&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-bd60bc35-240a-42ae-8c75-1bed96668b88&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-3bfd6ab7-de2e-4b3a-9535-4ae67a617a78&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-93f1f39a-0bdf-4137-be3c-eadadc154536&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-8f2eee83-385b-4a11-8dbe-989554b1d243&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-372802ef-e7a5-45c0-88f6-b003831af8b8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-0a7ccfe2-66eb-4596-8680-848646cb7683&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-5ebfcd26-58d3-4442-b917-445b134e1acb&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:true},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;fe2a9dd6-7017-4fbb-ba5d-9cedaac7818a&quot;}}}" />
      </map>
    </option>
  </component>
</project>