2025-07-01T16:06:12.292+0800	WARN	config_client/config_client.go:335	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-01T16:06:12.293+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-01T16:06:12.293+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-01T16:06:12.293+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-2f923ef0-486c-470e-a29f-50d4f611e590)
2025-07-01T16:06:12.293+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-01T16:06:12.293+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-01T16:06:12.293+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-01T16:06:12.293+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-2f923ef0-486c-470e-a29f-50d4f611e590 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-01T16:06:12.294+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-01T16:06:12.294+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-01T16:06:12.294+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-01T16:06:12.437+0800	INFO	rpc/rpc_client.go:337	config-0-2f923ef0-486c-470e-a29f-50d4f611e590 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751323213567_192.168.3.5_61559
2025-07-01T16:06:12.437+0800	INFO	rpc/rpc_client.go:486	config-0-2f923ef0-486c-470e-a29f-50d4f611e590 notify connected event to listeners , connectionId=1751323213567_192.168.3.5_61559
2025-07-01T16:06:12.437+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-01T16:07:48.158+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-07-01T16:07:48.158+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-07-01T16:07:48.158+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-ddead3db-a93c-425d-8c93-71bfd9646995)
2025-07-01T16:07:48.158+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-01T16:07:48.158+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-01T16:07:48.158+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-01T16:07:48.158+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-ddead3db-a93c-425d-8c93-71bfd9646995 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-01T16:07:48.158+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-01T16:07:48.158+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-01T16:07:48.158+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-01T16:07:48.299+0800	INFO	rpc/rpc_client.go:337	config-0-ddead3db-a93c-425d-8c93-71bfd9646995 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751323317422_192.168.3.5_61704
2025-07-01T16:07:48.299+0800	INFO	rpc/rpc_client.go:486	config-0-ddead3db-a93c-425d-8c93-71bfd9646995 notify connected event to listeners , connectionId=1751323317422_192.168.3.5_61704
2025-07-01T16:07:48.299+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-07-01T16:07:48.398+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<nanjing-dev>,serviceName:<brainHub.svc> with instance:<[{"instanceId":"","ip":"*************","port":8088,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-07-01T16:14:30.785+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<nanjing-dev>,serviceName:<brainHub.svc> with instance:<*************:8088@DEFAULT>
2025-09-17T21:11:09.930+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-09-17T21:11:09.930+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/brainHub.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-09-17T21:11:09.930+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-01830b0e-eeae-418e-8f5a-4c64d6b3c317)
2025-09-17T21:11:09.931+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T21:11:09.931+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T21:11:09.931+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T21:11:09.931+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-01830b0e-eeae-418e-8f5a-4c64d6b3c317 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T21:11:09.931+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T21:11:09.931+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T21:11:09.931+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T21:11:10.059+0800	INFO	rpc/rpc_client.go:337	config-0-01830b0e-eeae-418e-8f5a-4c64d6b3c317 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758114670018_192.168.3.254_53009
2025-09-17T21:11:10.060+0800	INFO	rpc/rpc_client.go:486	config-0-01830b0e-eeae-418e-8f5a-4c64d6b3c317 notify connected event to listeners , connectionId=1758114670018_192.168.3.254_53009
2025-09-17T21:11:10.060+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-09-17T21:11:10.110+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<nanjing-dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-09-17T21:11:10.113+0800	INFO	naming_cache/service_info_holder.go:96	service key:DEFAULT_GROUP@@dsh.svc@@DEFAULT was updated to:{"cacheMillis":10000,"hosts":[{"instanceId":"************#8087#DEFAULT#DEFAULT_GROUP@@dsh.svc","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dsh.svc","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}],"checksum":"","lastRefTime":1758114670174,"clusters":"DEFAULT","name":"dsh.svc","groupName":"DEFAULT_GROUP","valid":true,"allIPs":false,"reachProtectionThreshold":false}
2025-09-17T21:11:10.113+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-09-17T21:11:10.113+0800	WARN	naming_cache/service_info_holder.go:89	out of date data received, old-t: 1758114670174, new-t: 1758114670174
