2025-07-01T16:06:12.145+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-01T16:06:12.146+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55130
2025-07-01T16:06:12.146+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=8366e96b-80f9-442e-bd37-f6f88dcb098c)
2025-07-01T16:06:12.146+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-01T16:06:12.146+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-01T16:06:12.146+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-01T16:06:12.146+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 8366e96b-80f9-442e-bd37-f6f88dcb098c try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-01T16:06:12.146+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-01T16:06:12.146+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-01T16:06:12.146+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-01T16:06:12.147+0800	INFO	util/common.go:96	Local IP:**********
2025-07-01T16:06:12.290+0800	INFO	rpc/rpc_client.go:337	8366e96b-80f9-442e-bd37-f6f88dcb098c success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751323213410_192.168.3.5_61557
2025-07-01T16:06:12.291+0800	INFO	rpc/rpc_client.go:486	8366e96b-80f9-442e-bd37-f6f88dcb098c notify connected event to listeners , connectionId=1751323213410_192.168.3.5_61557
2025-07-01T16:07:47.997+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-07-01T16:07:47.998+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55815
2025-07-01T16:07:47.998+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=da47b5b5-701e-4dc7-be1e-a0c3da883ddf)
2025-07-01T16:07:47.999+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-01T16:07:47.999+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-01T16:07:47.999+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-01T16:07:47.999+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] da47b5b5-701e-4dc7-be1e-a0c3da883ddf try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-07-01T16:07:47.999+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-01T16:07:47.999+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-01T16:07:47.999+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-01T16:07:48.000+0800	INFO	util/common.go:96	Local IP:**********
2025-07-01T16:07:48.157+0800	INFO	rpc/rpc_client.go:337	da47b5b5-701e-4dc7-be1e-a0c3da883ddf success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751323317248_192.168.3.5_61702
2025-07-01T16:07:48.157+0800	INFO	rpc/rpc_client.go:486	da47b5b5-701e-4dc7-be1e-a0c3da883ddf notify connected event to listeners , connectionId=1751323317248_192.168.3.5_61702
2025-09-17T21:11:09.803+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-09-17T21:11:09.804+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55576
2025-09-17T21:11:09.804+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=47dd2892-8298-46c7-a42d-b11b28511260)
2025-09-17T21:11:09.804+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-09-17T21:11:09.804+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-09-17T21:11:09.804+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-09-17T21:11:09.804+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 47dd2892-8298-46c7-a42d-b11b28511260 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-09-17T21:11:09.804+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-09-17T21:11:09.804+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-09-17T21:11:09.804+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-09-17T21:11:09.805+0800	INFO	util/common.go:96	Local IP:**********
2025-09-17T21:11:09.927+0800	INFO	rpc/rpc_client.go:337	47dd2892-8298-46c7-a42d-b11b28511260 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1758114669886_192.168.3.254_53005
